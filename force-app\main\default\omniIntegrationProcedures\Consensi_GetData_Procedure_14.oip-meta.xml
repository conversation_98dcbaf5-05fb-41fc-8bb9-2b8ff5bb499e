<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{
    &quot;RecordId&quot;: &quot;a1i9V000005CC0vQAG&quot;
}</customJavaScript>
    <description>@GP Fix sulla transcodifica dei valori + Aggiunto fiscalCode User in Output + Aggiunte info sui permessi</description>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>true</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>true</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>Procedure</language>
    <name>Consensi Anagrafica</name>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CalculateCheckBox</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;%RetriveConsensi:OptInInformation%&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;ConsensiTransformCheckBox&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>DataRaptor Transform Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>callFeaHelper</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;type&quot; : &quot;=&apos;FES&apos;&quot;,
    &quot;ciu&quot; : &quot;=TOSTRING(%RetriveConsensi:ciu%)&quot;,
    &quot;userId&quot; : &quot;=$Vlocity.UserId&quot;,
    &quot;recordId&quot; : &quot;=%RecordId%&quot;,
    &quot;cellulare&quot; : &quot;%GetAccAgencyDet:mobile%&quot;,
    &quot;email&quot; : &quot;%GetAccAgencyDet:email%&quot;
  },
  &quot;additionalOutput&quot; : {
    &quot;compagnia&quot; : &quot;=%RetriveConsensi:compagniaCode%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;FEA_Helper&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;remoteMethod&quot; : &quot;createBarcodeAndXML&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;RemoteAction3&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>6.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetAccAgencyDet</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;UniGetAccAgeDetInfo&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;userIdAzienda&quot;,
    &quot;element&quot; : &quot;GetUserFiscalCode:userIdAzienda&quot;
  }, {
    &quot;inputParam&quot; : &quot;ciu&quot;,
    &quot;element&quot; : &quot;RetriveConsensi:ciu&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetPermissionInfo</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;customPermissions&quot; : [ &quot;RevocaPrivacy&quot; ]
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;checkCp&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;OmnistudioUtils&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;remoteMethod&quot; : &quot;checkCustomPermissions&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;RemoteAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetUserFiscalCode</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;FEA_Helper&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;remoteMethod&quot; : &quot;getUserFiscalCode&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;RemoteAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>RetriveConsensi</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;RetriveConsensiByAccountDet&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>Response00</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%RetriveConsensi:SourceSystemConsentCode% == \&quot;00\&quot; &quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;StatoPrivacy&quot; : &quot;=\&quot;Mancante\&quot;&quot;,
    &quot;TipoConsenso&quot; : &quot;=\&quot;Contatto per appuntamento\&quot;&quot;,
    &quot;DataInizio&quot; : &quot;=%RetriveConsensi:SourceSystemConsentEffectiveDate%&quot;,
    &quot;Checkbox&quot; : &quot;=%CalculateCheckBox%&quot;,
    &quot;compagnia&quot; : &quot;=%RetriveConsensi:compagnia%&quot;,
    &quot;ciu&quot; : &quot;=%RetriveConsensi:ciu%&quot;,
    &quot;DataAggiornamento&quot; : &quot;=NOW(\&quot;dd-MM-yyyy\&quot;)&quot;,
    &quot;fiscalCode&quot; : &quot;%GetUserFiscalCode:userFiscalCode%&quot;,
    &quot;checkCp&quot; : &quot;%checkCp%&quot;,
    &quot;FeaHelper&quot; : &quot;%callFeaHelper%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;ResponseMancante&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>5.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseCompleta</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%RetriveConsensi:SourceSystemConsentCode% == \&quot;06\&quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;StatoPrivacy&quot; : &quot;=\&quot;Completo\&quot;&quot;,
    &quot;TipoConsenso&quot; : &quot;=\&quot;Commerciale, Profilazione e Gruppo\&quot;&quot;,
    &quot;DataInizio&quot; : &quot;=%RetriveConsensi:SourceSystemConsentEffectiveDate%&quot;,
    &quot;Checkbox&quot; : &quot;=%CalculateCheckBox%&quot;,
    &quot;compagnia&quot; : &quot;=%RetriveConsensi:compagnia%&quot;,
    &quot;ciu&quot; : &quot;=%RetriveConsensi:ciu%&quot;,
    &quot;DataAggiornamento&quot; : &quot;=NOW(\&quot;dd-MM-yyyy\&quot;)&quot;,
    &quot;fiscalCode&quot; : &quot;%GetUserFiscalCode:userFiscalCode%&quot;,
    &quot;checkCp&quot; : &quot;%checkCp%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;ResponseCompleta&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>11.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseDaAggiornare01</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%RetriveConsensi:SourceSystemConsentCode% == \&quot;01\&quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;StatoPrivacy&quot; : &quot;=\&quot;Da Aggiornare\&quot;&quot;,
    &quot;TipoConsenso&quot; : &quot;=\&quot;Commerciale\&quot;&quot;,
    &quot;DataInizio&quot; : &quot;=%RetriveConsensi:SourceSystemConsentEffectiveDate%&quot;,
    &quot;Checkbox&quot; : &quot;=%CalculateCheckBox%&quot;,
    &quot;compagnia&quot; : &quot;=%RetriveConsensi:compagnia%&quot;,
    &quot;ciu&quot; : &quot;=%RetriveConsensi:ciu%&quot;,
    &quot;DataAggiornamento&quot; : &quot;=NOW(\&quot;dd-MM-yyyy\&quot;)&quot;,
    &quot;fiscalCode&quot; : &quot;%GetUserFiscalCode:userFiscalCode%&quot;,
    &quot;checkCp&quot; : &quot;%checkCp%&quot;,
    &quot;FeaHelper&quot; : &quot;%callFeaHelper%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;ResponseDaAggiornare&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>6.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseDaAggiornare02</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%RetriveConsensi:SourceSystemConsentCode% == \&quot;02\&quot; &quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;StatoPrivacy&quot; : &quot;=\&quot;Da Aggiornare\&quot;&quot;,
    &quot;TipoConsenso&quot; : &quot;=\&quot;Commerciale e Profilazione\&quot;&quot;,
    &quot;DataInizio&quot; : &quot;=%RetriveConsensi:SourceSystemConsentEffectiveDate%&quot;,
    &quot;Checkbox&quot; : &quot;=%CalculateCheckBox%&quot;,
    &quot;compagnia&quot; : &quot;=%RetriveConsensi:compagnia%&quot;,
    &quot;ciu&quot; : &quot;=%RetriveConsensi:ciu%&quot;,
    &quot;DataAggiornamento&quot; : &quot;=NOW(\&quot;dd-MM-yyyy\&quot;)&quot;,
    &quot;fiscalCode&quot; : &quot;%GetUserFiscalCode:userFiscalCode%&quot;,
    &quot;checkCp&quot; : &quot;%checkCp%&quot;,
    &quot;FeaHelper&quot; : &quot;%callFeaHelper%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;ResponseDaAggiornare&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>7.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseDaAggiornare03</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%RetriveConsensi:SourceSystemConsentCode% == \&quot;03\&quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;StatoPrivacy&quot; : &quot;=\&quot;Da Aggiornare\&quot;&quot;,
    &quot;TipoConsenso&quot; : &quot;=\&quot;Commerciale\&quot;&quot;,
    &quot;DataInizio&quot; : &quot;=%RetriveConsensi:SourceSystemConsentEffectiveDate%&quot;,
    &quot;Checkbox&quot; : &quot;=%CalculateCheckBox%&quot;,
    &quot;compagnia&quot; : &quot;=%RetriveConsensi:compagnia%&quot;,
    &quot;ciu&quot; : &quot;=%RetriveConsensi:ciu%&quot;,
    &quot;DataAggiornamento&quot; : &quot;=NOW(\&quot;dd-MM-yyyy\&quot;)&quot;,
    &quot;fiscalCode&quot; : &quot;%GetUserFiscalCode:userFiscalCode%&quot;,
    &quot;checkCp&quot; : &quot;%checkCp%&quot;,
    &quot;FeaHelper&quot; : &quot;%callFeaHelper%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;ResponseDaAggiornare&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>8.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseDaAggiornare04</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%RetriveConsensi:SourceSystemConsentCode% == \&quot;04\&quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;StatoPrivacy&quot; : &quot;=\&quot;Da Aggiornare\&quot;&quot;,
    &quot;TipoConsenso&quot; : &quot;=\&quot;Commerciale e Profilazione\&quot;&quot;,
    &quot;DataInizio&quot; : &quot;=%RetriveConsensi:SourceSystemConsentEffectiveDate%&quot;,
    &quot;Checkbox&quot; : &quot;=%CalculateCheckBox%&quot;,
    &quot;compagnia&quot; : &quot;=%RetriveConsensi:compagnia%&quot;,
    &quot;ciu&quot; : &quot;=%RetriveConsensi:ciu%&quot;,
    &quot;DataAggiornamento&quot; : &quot;=NOW(\&quot;dd-MM-yyyy\&quot;)&quot;,
    &quot;fiscalCode&quot; : &quot;%GetUserFiscalCode:userFiscalCode%&quot;,
    &quot;checkCp&quot; : &quot;%checkCp%&quot;,
    &quot;FeaHelper&quot; : &quot;%callFeaHelper%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;ResponseDaAggiornare&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>9.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseDaAggiornare05</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%RetriveConsensi:SourceSystemConsentCode% == \&quot;05\&quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;StatoPrivacy&quot; : &quot;=\&quot;Da Aggiornare\&quot;&quot;,
    &quot;TipoConsenso&quot; : &quot;=\&quot;Commerciale e Gruppo\&quot;&quot;,
    &quot;DataInizio&quot; : &quot;=%RetriveConsensi:SourceSystemConsentEffectiveDate%&quot;,
    &quot;Checkbox&quot; : &quot;=%CalculateCheckBox%&quot;,
    &quot;compagnia&quot; : &quot;=%RetriveConsensi:compagnia%&quot;,
    &quot;ciu&quot; : &quot;=%RetriveConsensi:ciu%&quot;,
    &quot;DataAggiornamento&quot; : &quot;=NOW(\&quot;dd-MM-yyyy\&quot;)&quot;,
    &quot;fiscalCode&quot; : &quot;%GetUserFiscalCode:userFiscalCode%&quot;,
    &quot;checkCp&quot; : &quot;%checkCp%&quot;,
    &quot;FeaHelper&quot; : &quot;%callFeaHelper%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;ResponseDaAggiornare&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>10.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseMancante</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%RetriveConsensi:SourceSystemConsentCode% == \&quot;CP\&quot; || %RetriveConsensi:SourceSystemConsentCode% == \&quot;CM\&quot; || %RetriveConsensi:SourceSystemConsentCode% == \&quot;CG\&quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;StatoPrivacy&quot; : &quot;=\&quot;Mancante\&quot;&quot;,
    &quot;TipoConsenso&quot; : &quot;=\&quot;Mancante\&quot;&quot;,
    &quot;DataInizio&quot; : &quot;=%RetriveConsensi:SourceSystemConsentEffectiveDate%&quot;,
    &quot;Checkbox&quot; : &quot;=%CalculateCheckBox%&quot;,
    &quot;compagnia&quot; : &quot;=%RetriveConsensi:compagnia%&quot;,
    &quot;ciu&quot; : &quot;=%RetriveConsensi:ciu%&quot;,
    &quot;DataAggiornamento&quot; : &quot;=NOW(\&quot;dd-MM-yyyy\&quot;)&quot;,
    &quot;fiscalCode&quot; : &quot;%GetUserFiscalCode:userFiscalCode%&quot;,
    &quot;checkCp&quot; : &quot;%checkCp%&quot;,
    &quot;FeaHelper&quot; : &quot;%callFeaHelper%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;ResponseMancante&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseMancanteEmpty</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%RetriveConsensi:SourceSystemConsentCode% == \&quot;NULL\&quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;StatoPrivacy&quot; : &quot;=\&quot;Mancante\&quot;&quot;,
    &quot;TipoConsenso&quot; : &quot;=\&quot;-\&quot;&quot;,
    &quot;DataInizio&quot; : &quot;=%RetriveConsensi:SourceSystemConsentEffectiveDate%&quot;,
    &quot;Checkbox&quot; : &quot;=%CalculateCheckBox%&quot;,
    &quot;compagnia&quot; : &quot;=%RetriveConsensi:ciu%&quot;,
    &quot;ciu&quot; : &quot;=%RetriveConsensi:ciu%&quot;,
    &quot;DataAggiornamento&quot; : &quot;=NOW(\&quot;dd-MM-yyyy\&quot;)&quot;,
    &quot;fiscalCode&quot; : &quot;%GetUserFiscalCode:userFiscalCode%&quot;,
    &quot;checkCp&quot; : &quot;%checkCp%&quot;,
    &quot;FeaHelper&quot; : &quot;%callFeaHelper%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;ResponseMancante&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseOptOut</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%RetriveConsensi:OptOutType% != \&quot;NULL\&quot; &amp;&amp; %RetriveConsensi:OptOutEffectiveDate% &lt;= TODAY() &amp;&amp; (RetriveConsensi:OptOutEndDate == \&quot;NULL\&quot; || RetriveConsensi:OptOutEndDate &gt;= TODAY())&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;StatoPrivacy&quot; : &quot;=\&quot;Revocata / Opt-Out\&quot;&quot;,
    &quot;TipoConsenso&quot; : &quot;=\&quot;Opt-Out\&quot;&quot;,
    &quot;DataInizio&quot; : &quot;=%RetriveConsensi:OptOutEffectiveDate%&quot;,
    &quot;Checkbox&quot; : &quot;=%CalculateCheckBox%&quot;,
    &quot;compagnia&quot; : &quot;=%RetriveConsensi:compagnia%&quot;,
    &quot;ciu&quot; : &quot;=%RetriveConsensi:ciu%&quot;,
    &quot;DataAggiornamento&quot; : &quot;=NOW(\&quot;dd-MM-yyyy\&quot;)&quot;,
    &quot;fiscalCode&quot; : &quot;%GetUserFiscalCode:userFiscalCode%&quot;,
    &quot;checkCp&quot; : &quot;%checkCp%&quot;,
    &quot;FeaHelper&quot; : &quot;%callFeaHelper%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseRevoca</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%RetriveConsensi:SourceSystemConsentEndDate% != \&quot;NULL\&quot; &amp;&amp; %RetriveConsensi:SourceSystemConsentEndDate% &lt;= TODAY() ||(%RetriveConsensi:SourceSystemConsentCode% == \&quot;REV\&quot;) &quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;StatoPrivacy&quot; : &quot;=\&quot;Revocata\&quot;&quot;,
    &quot;TipoConsenso&quot; : &quot;=\&quot;Revoca\&quot;&quot;,
    &quot;DataInizio&quot; : &quot;=\&quot;-\&quot;&quot;,
    &quot;Checkbox&quot; : &quot;=%CalculateCheckBox%&quot;,
    &quot;compagnia&quot; : &quot;=%RetriveConsensi:compagnia%&quot;,
    &quot;ciu&quot; : &quot;=%RetriveConsensi:ciu%&quot;,
    &quot;DataAggiornamento&quot; : &quot;=NOW(\&quot;dd-MM-yyyy\&quot;)&quot;,
    &quot;fiscalCode&quot; : &quot;%GetUserFiscalCode:userFiscalCode%&quot;,
    &quot;checkCp&quot; : &quot;%checkCp%&quot;,
    &quot;FeaHelper&quot; : &quot;%callFeaHelper%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SplitResponse</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;isIfElseBlock&quot; : true,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock6&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>7.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessKey>Consensi_GetData</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;linkToExternalObject&quot; : &quot;&quot;,
  &quot;trackingCustomData&quot; : { },
  &quot;includeAllActionsInResponse&quot; : false,
  &quot;columnsPropertyMap&quot; : [ ],
  &quot;relationshipFieldsMap&quot; : [ ],
  &quot;labelSingular&quot; : &quot;&quot;,
  &quot;labelPlural&quot; : &quot;&quot;,
  &quot;description&quot; : &quot;&quot;,
  &quot;nameColumn&quot; : &quot;&quot;,
  &quot;rollbackOnError&quot; : false,
  &quot;chainableQueriesLimit&quot; : 50,
  &quot;chainableDMLStatementsLimit&quot; : null,
  &quot;chainableCpuLimit&quot; : 2000,
  &quot;chainableHeapSizeLimit&quot; : null,
  &quot;chainableDMLRowsLimit&quot; : null,
  &quot;chainableQueryRowsLimit&quot; : null,
  &quot;chainableSoslQueriesLimit&quot; : null,
  &quot;chainableActualTimeLimit&quot; : null,
  &quot;additionalChainableResponse&quot; : { },
  &quot;queueableChainableQueriesLimit&quot; : 120,
  &quot;queueableChainableCpuLimit&quot; : 40000,
  &quot;queueableChainableHeapSizeLimit&quot; : 6,
  &quot;ttlMinutes&quot; : 5,
  &quot;mockResponseMap&quot; : { }
}</propertySetConfig>
    <subType>GetData</subType>
    <type>Consensi</type>
    <uniqueName>Consensi_GetData_Procedure_14</uniqueName>
    <versionNumber>14.0</versionNumber>
    <webComponentKey>43786dc9-7daf-664d-1e99-c3c3210e7382</webComponentKey>
</OmniIntegrationProcedure>
