<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Add_Permission_Set_Name_to_List</name>
        <label>Add Permission Set Name to List</label>
        <locationX>424</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>MandatoPermissionSetNameList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>User_Permission_Set_Loop.PermissionSet.Name</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>User_Permission_Set_Loop</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Disable_all_choices</name>
        <label>Disable all choices</label>
        <locationX>336</locationX>
        <locationY>1058</locationY>
        <assignmentItems>
            <assignToReference>DisableAllChoices</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_chosen_Account</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Enable_all_choices</name>
        <label>Enable all choices</label>
        <locationX>336</locationX>
        <locationY>842</locationY>
        <assignmentItems>
            <assignToReference>DisableAllChoices</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Product_Screen</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>altriProdotti</name>
        <choiceText>{!formattedSOpen}&lt;img src=&quot;https://unipol--devcap.sandbox.file.force.com/sfc/servlet.shepherd/version/download/0689Q00000A8e49?asPdf=false&amp;amp;operationContext=CHATTER&quot; alt=&quot;altriProdotti.png&quot;&gt;{!newLine}{!altriProdottiText}﻿{!formattedSClose}</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>MandatoAltriProdotti</stringValue>
        </value>
    </choices>
    <choices>
        <name>move</name>
        <choiceText>{!formattedSOpen}&lt;img src=&quot;https://unipol--devcap.sandbox.file.force.com/sfc/servlet.shepherd/version/download/0689Q00000A8eDp?asPdf=false&amp;amp;operationContext=CHATTER&quot; alt=&quot;move.png&quot;&gt;{!newLine}{!moveText}﻿{!formattedSClose}</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>MandatoMove</stringValue>
        </value>
    </choices>
    <choices>
        <name>rental</name>
        <choiceText>{!formattedSOpen}&lt;img src=&quot;https://unipol--devcap.sandbox.file.force.com/sfc/servlet.shepherd/version/download/0689Q00000A8Z9K?asPdf=false&amp;amp;operationContext=CHATTER&quot; alt=&quot;rental.png&quot;&gt;{!newLine}{!rentalText}﻿{!formattedSClose}</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>MandatoRental</stringValue>
        </value>
    </choices>
    <choices>
        <name>salute</name>
        <choiceText>{!formattedSOpen}{!image_unisalute}{!newLine}{!saluteText}{!formattedSClose}</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>MandatoUniSalute</stringValue>
        </value>
    </choices>
    <choices>
        <name>salute_per_te</name>
        <choiceText>{!formattedSOpen}{!image_unisalute}{!newLine}{!salutePerTeText}{!formattedSClose}</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>MandatoUniSalute</stringValue>
        </value>
    </choices>
    <choices>
        <name>unica</name>
        <choiceText>{!formattedSOpen}{!image_unica}{!newLine}{!unicaText}{!formattedSClose}</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>MandatoUnipolSai</stringValue>
        </value>
    </choices>
    <choices>
        <name>unipolTeh</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;{!formattedSOpen}{!newLine}{!unipolTehText}﻿{!formattedSClose}&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>mandatoUnipolTeh</stringValue>
        </value>
    </choices>
    <choices>
        <name>vita</name>
        <choiceText>{!formattedSOpen}&lt;img src=&quot;https://unipol--devcap.sandbox.file.force.com/sfc/servlet.shepherd/version/download/0689Q00000A8eAb?asPdf=false&amp;amp;operationContext=CHATTER&quot; alt=&quot;vitaProtezione.png&quot;&gt;{!newLine}{!vitaText}﻿{!formattedSClose}</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>MandatoVita</stringValue>
        </value>
    </choices>
    <constants>
        <name>altriProdottiText</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Qui puoi trovare gli ambiti di copertura danni che non sono inseriti dentro Unica, come ad esempio Commercio, Impresa, Condominio, Professionisti</stringValue>
        </value>
    </constants>
    <constants>
        <name>formattedSClose</name>
        <dataType>String</dataType>
        <value>
            <stringValue>&lt;/span&gt;</stringValue>
        </value>
    </constants>
    <constants>
        <name>formattedSOpen</name>
        <dataType>String</dataType>
        <value>
            <stringValue>&lt;span style=&quot;display: inline-block;align-items: center;padding: 7px;margin:1%;border: 1px solid #ddd;border-radius: 5px;background-color: #fff;cursor: pointer;transition: background-color 0.3s, border-color 0.3s;width: 20%;&quot;&gt;</stringValue>
        </value>
    </constants>
    <constants>
        <name>image_unica</name>
        <dataType>String</dataType>
        <value>
            <stringValue>&lt;img src=&quot;/resource/Images_Vetrina_Prodotti/unica.png&quot; alt=&quot;unica&quot; /&gt;</stringValue>
        </value>
    </constants>
    <constants>
        <name>image_unisalute</name>
        <dataType>String</dataType>
        <value>
            <stringValue> &lt;img src=&quot;/resource/Images_Vetrina_Prodotti/unisalute.png&quot; alt=&quot;unisalute&quot;  /&gt;</stringValue>
        </value>
    </constants>
    <constants>
        <name>moveText</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Qui puoi trovare il servizio di Telepedaggio per saltare la coda al casello che fa risparmiare tempo ai tuoi clienti. In più, possono risparmiare anche sul canone Tech</stringValue>
        </value>
    </constants>
    <constants>
        <name>newLine</name>
        <dataType>String</dataType>
        <value>
            <stringValue>&lt;br/&gt;</stringValue>
        </value>
    </constants>
    <constants>
        <name>rentalText</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Qui puoi personalizzare un&apos;offerta in base alle esigenze del cliente, con un canone mensile fisso e una vasta gamma di servizi inclusi per semplificare la gestione della mobilità</stringValue>
        </value>
    </constants>
    <constants>
        <name>salutePerTeText</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Qui puoi trovare la nuova offerta con copertura modulare e personalizzata di UniSalute Per Te</stringValue>
        </value>
    </constants>
    <constants>
        <name>saluteText</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Qui puoi trovare tutte le tipologie di copertura incluse nell&apos;offerta Unisalute, come ad esempio PMI, Invalidità, Rinnovo Garantito, RSM Sanicard...</stringValue>
        </value>
    </constants>
    <constants>
        <name>UnicaImageLink</name>
        <dataType>String</dataType>
        <value>
            <stringValue>&lt;img src=&quot;https://unipol--dev.sandbox.file.force.com/sfc/servlet.shepherd/version/download/0699Q00000AcSjtQAF?asPdf=false&amp;amp;operationContext=CHATTER&quot; alt=&quot;unica.png&quot;&gt;</stringValue>
        </value>
    </constants>
    <constants>
        <name>unicaText</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Qui puoi trovare l&apos;offerta multiambito di Unipol: un unico prodotto che comprende Veicoli, Mobilità, Casa, Famiglia, Viaggio, Cane, Gatto, Infortuni e Salute</stringValue>
        </value>
    </constants>
    <constants>
        <name>unipolTehText</name>
        <dataType>String</dataType>
        <value>
            <stringValue>TODO METTI INFO SU Telepedaggio</stringValue>
        </value>
    </constants>
    <constants>
        <name>vitaText</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Qui puoi trovare gli ambiti di copertura vita e protezione, come ad esempio TCM, LTC, Risparmio e Investimento</stringValue>
        </value>
    </constants>
    <decisions>
        <name>ifOpportunity</name>
        <label>ifOpportunity</label>
        <locationX>182</locationX>
        <locationY>1598</locationY>
        <defaultConnector>
            <targetReference>FEI_Quick_Action_Vetrina_Prodotti_Subflow</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getOpportunity</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Handle_Event_Update_SLA_Expiry_Date_Opportunity_Subflow_Flow_1</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>isValidId</name>
        <label>isValidId</label>
        <locationX>336</locationX>
        <locationY>1382</locationY>
        <defaultConnector>
            <targetReference>FEI_Quick_Action_Vetrina_Prodotti_Subflow</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Valid_recordId</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>{recordId}</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getOpportunity</targetReference>
            </connector>
            <label>Valid recordId</label>
        </rules>
    </decisions>
    <decisions>
        <name>Mandato_Permission_Set_Check</name>
        <label>Mandato Permission Set Check</label>
        <locationX>556</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>User_Permission_Set_Loop</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Permission_set_is_Mandato</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>User_Permission_Set_Loop.PermissionSet.Name</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Mandato</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Add_Permission_Set_Name_to_List</targetReference>
            </connector>
            <label>Permission set is Mandato</label>
        </rules>
    </decisions>
    <description>Fix Data e Ora Expiring Date</description>
    <environments>Default</environments>
    <formulas>
        <name>ChosenProduct</name>
        <dataType>String</dataType>
        <expression>BLANKVALUE({!Seleziona_Prodotto_salute}, BLANKVALUE({!Seleziona_Prodotto_Unica}, &apos;&apos;))</expression>
    </formulas>
    <formulas>
        <name>DisableSearchAccount</name>
        <dataType>Boolean</dataType>
        <expression>!ISBLANK({!recordId})</expression>
    </formulas>
    <formulas>
        <name>DisableUnipolSaiChoice</name>
        <dataType>Boolean</dataType>
        <expression>OR({!DisableAllChoices},
!ISBLANK({!Seleziona_Prodotto_salute}), !ISBLANK({!Seleziona_Prodotto_Vita}), !ISBLANK({!Seleziona_Prodotto_UnipolTeh}), !ISBLANK({!Seleziona_Prodotto_salute_per_te})
)</expression>
    </formulas>
    <formulas>
        <name>DisableUnipolTehChoice</name>
        <dataType>Boolean</dataType>
        <expression>OR({!DisableAllChoices},
!ISBLANK({!Seleziona_Prodotto_Unica}), !ISBLANK({!Seleziona_Prodotto_salute}), !ISBLANK({!Seleziona_Prodotto_Vita}), !ISBLANK({!Seleziona_Prodotto_salute_per_te})
)</expression>
    </formulas>
    <formulas>
        <name>DisableUniSaluteChoice</name>
        <dataType>Boolean</dataType>
        <expression>OR({!DisableAllChoices},
!ISBLANK({!Seleziona_Prodotto_Unica}), !ISBLANK({!Seleziona_Prodotto_Vita}), !ISBLANK({!Seleziona_Prodotto_UnipolTeh}), !ISBLANK({!Seleziona_Prodotto_salute_per_te})
)</expression>
    </formulas>
    <formulas>
        <name>DisableUniSalutePerTeChoise</name>
        <dataType>Boolean</dataType>
        <expression>OR({!DisableAllChoices},
!ISBLANK({!Seleziona_Prodotto_Unica}), !ISBLANK({!Seleziona_Prodotto_Vita}), !ISBLANK({!Seleziona_Prodotto_UnipolTeh}), !ISBLANK({!Seleziona_Prodotto_salute})
)</expression>
    </formulas>
    <formulas>
        <name>DisableVitaChoice</name>
        <dataType>Boolean</dataType>
        <expression>OR({!DisableAllChoices},
!ISBLANK({!Seleziona_Prodotto_Unica}), !ISBLANK({!Seleziona_Prodotto_salute}), !ISBLANK({!Seleziona_Prodotto_UnipolTeh}), !ISBLANK({!Seleziona_Prodotto_salute_per_te})
)</expression>
    </formulas>
    <formulas>
        <name>feiSubflow_customerId</name>
        <dataType>String</dataType>
        <expression>{!Get_chosen_Account.ExternalId__c}</expression>
    </formulas>
    <formulas>
        <name>FEISubflow_FEIID</name>
        <dataType>String</dataType>
        <expression>IF(
  {!ChosenProduct} = {!Seleziona_Prodotto_salute}, &apos;RE.PRODOTTI&apos;,
  IF(
    {!ChosenProduct} = {!Seleziona_Prodotto_Unica}, &apos;UNICA.VETRINA.PRODOTTI&apos;,
    IF(
      {!ChosenProduct} = {!Seleziona_Prodotto_Vita}, &apos;VITA.PRODOTTI&apos;,
      IF(
        {!ChosenProduct} = {!Seleziona_Prodotto_UnipolTeh}, &apos;TELEPEDAGGIO.PRODOTTI&apos;,
        IF(
          {!ChosenProduct} = {!Seleziona_Prodotto_salute_per_te}, &apos;USNEXT.NUOVO.PREVENTIVO&apos;,
          &apos;&apos; 
        )
      )
    )
  )
)</expression>
    </formulas>
    <formulas>
        <name>SlaExpiryDate</name>
        <dataType>DateTime</dataType>
        <expression>DATETIMEVALUE(TODAY()) + (21/24) + (59/24/60) + {!SlaRemainingDays}</expression>
    </formulas>
    <interviewLabel>TestVetrina {!$Flow.CurrentDateTime}</interviewLabel>
    <label>VetrinaProdotti</label>
    <loops>
        <name>User_Permission_Set_Loop</name>
        <label>User Permission Set Loop</label>
        <locationX>336</locationX>
        <locationY>242</locationY>
        <collectionReference>Get_User_Permission_Set_Assignments</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Mandato_Permission_Set_Check</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Intro_Screen</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_chosen_Account</name>
        <label>Get chosen Account</label>
        <locationX>336</locationX>
        <locationY>1166</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>confirmChoices</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>chooseAccount.chosenAccountId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_User_Permission_Set_Assignments</name>
        <label>Get User Permission Set Assignments</label>
        <locationX>336</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>User_Permission_Set_Loop</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AssigneeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>PermissionSetAssignment</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getOpportunity</name>
        <label>getOpportunity</label>
        <locationX>182</locationX>
        <locationY>1490</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>ifOpportunity</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>updateOpportunity</name>
        <label>updateOpportunity</label>
        <locationX>50</locationX>
        <locationY>1814</locationY>
        <connector>
            <targetReference>FEI_Quick_Action_Vetrina_Prodotti_Subflow</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getOpportunity.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>WorkingSLAExpiryDate__c</field>
            <value>
                <elementReference>SlaExpiryDate</elementReference>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordUpdates>
    <screens>
        <name>confirmChoices</name>
        <label>Conferma le scelte fatte...</label>
        <locationX>336</locationX>
        <locationY>1274</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>isValidId</targetReference>
        </connector>
        <fields>
            <name>SoggettoSelezionato</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 14px;&quot;&gt;Hai selezionato il seguente Soggetto &lt;/strong&gt;&lt;/p&gt;&lt;p&gt;{!Get_chosen_Account.Name}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>ProdottoSelezionato</name>
            <fieldText>&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;font-size: 14px; background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Hai scelto il seguente catalogo: &lt;/strong&gt;&lt;/p&gt;&lt;p&gt;{!Seleziona_Prodotto_Unica}&lt;/p&gt;&lt;p&gt;{!Seleziona_Prodotto_salute_per_te}&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;{!Seleziona_Prodotto_salute}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;{!Seleziona_Prodotto_Vita}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(68, 68, 68); background-color: rgb(255, 255, 255);&quot;&gt;{!Seleziona_Prodotto_UnipolTeh}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Intro_Screen</name>
        <label>Intro Screen</label>
        <locationX>336</locationX>
        <locationY>734</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Enable_all_choices</targetReference>
        </connector>
        <nextOrFinishButtonLabel>Scegli Prodotto</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Product_Screen</name>
        <label>Product Screen</label>
        <locationX>336</locationX>
        <locationY>950</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Disable_all_choices</targetReference>
        </connector>
        <fields>
            <name>chooseAccount</name>
            <extensionName>c:flowVetrinaProdotti_chooseAccount</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>recordId</name>
                <value>
                    <elementReference>recordId</elementReference>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>SelezionaProdotto</name>
            <fieldText>&lt;p&gt;Seleziona Prodotto&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Seleziona_Prodotto_salute_per_te</name>
            <choiceReferences>salute_per_te</choiceReferences>
            <dataType>String</dataType>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <elementReference>DisableUniSalutePerTeChoise</elementReference>
            </isDisabled>
            <isRequired>false</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>MandatoPermissionSetNameList</leftValueReference>
                    <operator>Contains</operator>
                    <rightValue>
                        <stringValue>MandatoUniSalute</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Seleziona_Prodotto_salute</name>
            <choiceReferences>salute</choiceReferences>
            <dataType>String</dataType>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <elementReference>DisableUniSaluteChoice</elementReference>
            </isDisabled>
            <isRequired>false</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>MandatoPermissionSetNameList</leftValueReference>
                    <operator>Contains</operator>
                    <rightValue>
                        <stringValue>MandatoUniSalute</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Seleziona_Prodotto_Unica</name>
            <choiceReferences>unica</choiceReferences>
            <dataType>String</dataType>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <elementReference>DisableUnipolSaiChoice</elementReference>
            </isDisabled>
            <isRequired>false</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>MandatoPermissionSetNameList</leftValueReference>
                    <operator>Contains</operator>
                    <rightValue>
                        <stringValue>MandatoUnipolSai</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Seleziona_Prodotto_Vita</name>
            <choiceReferences>vita</choiceReferences>
            <dataType>String</dataType>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <elementReference>DisableVitaChoice</elementReference>
            </isDisabled>
            <isRequired>false</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>MandatoPermissionSetNameList</leftValueReference>
                    <operator>Contains</operator>
                    <rightValue>
                        <stringValue>MandatoVita</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Seleziona_Prodotto_UnipolTeh</name>
            <choiceReferences>unipolTeh</choiceReferences>
            <dataType>String</dataType>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <elementReference>DisableUnipolTehChoice</elementReference>
            </isDisabled>
            <isRequired>false</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>MandatoPermissionSetNameList</leftValueReference>
                    <operator>Contains</operator>
                    <rightValue>
                        <stringValue>MandatoUnipolTeh</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>210</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_User_Permission_Set_Assignments</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>FEI_Quick_Action_Vetrina_Prodotti_Subflow</name>
        <label>FEI Quick Action Vetrina Prodotti Subflow</label>
        <locationX>336</locationX>
        <locationY>2090</locationY>
        <flowName>FEIQuickActionVetrinaProdotti</flowName>
        <inputAssignments>
            <name>customerID</name>
            <value>
                <elementReference>feiSubflow_customerId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>FEIID</name>
            <value>
                <elementReference>FEISubflow_FEIID</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>Handle_Event_Update_SLA_Expiry_Date_Opportunity_Subflow_Flow_1</name>
        <label>Handle Event - Update SLA Expiry Date Opportunity Subflow Flow 1</label>
        <locationX>50</locationX>
        <locationY>1706</locationY>
        <connector>
            <targetReference>updateOpportunity</targetReference>
        </connector>
        <flowName>Handle_Event_Update_SLA_Expiry_Date_Opportunity_Subflow</flowName>
        <inputAssignments>
            <name>OpportunityInput</name>
            <value>
                <elementReference>getOpportunity</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>SlaRemainingDays</assignToReference>
            <name>remainingDaysSLA</name>
        </outputAssignments>
    </subflows>
    <variables>
        <name>DisableAllChoices</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>MandatoPermissionSetNameList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>permissionsMandati</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>PermissionSet</objectType>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>SlaRemainingDays</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
    </variables>
</Flow>
