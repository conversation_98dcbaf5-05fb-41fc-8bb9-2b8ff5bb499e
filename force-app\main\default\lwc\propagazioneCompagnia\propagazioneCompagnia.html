<template>
    <c-loader lwc:ref="loader"></c-loader>
    <div class="slds-grid slds-wrap slds-is-relative" if:true={initDone}>
        <div class="slds-col slds-size_6-of-12 slds-p-around_xx-small">
            <lightning-input 
                type="checkbox" 
                label="Propaga anche su altre compagnie" 
                name="propaga" 
                disabled={propagazioneCompagniaDisabled}
                checked={propaga} 
                onchange={handleCheckboxChange}>
            </lightning-input>
        </div>
        <div class="slds-col slds-size_6-of-12 slds-p-around_xx-small" if:true={propaga}>
            <lightning-combobox variant="label-hidden"
                name="companyList"
                class="slds-p-horizontal_xx-small"
                placeholder="Seleziona compagnia"
                value={optionsCompagnieFilter}
                onchange={handleSelectChange}
                options={optionsCompagnieFilter}>
            </lightning-combobox>
            <template for:each={selectedCompany} for:item="company">
                <lightning-pill key={company.value}
                    label={company.label}
                    onremove={clear}
                    name={company.value}>
                </lightning-pill>
            </template>
        </div>
    </div>
</template>