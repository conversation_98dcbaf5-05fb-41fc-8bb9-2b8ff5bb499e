@isTest
public class AccountDetailsNPIBatchTest {
    @isTest
    static void testBatchExecution() {
        // Create test data
        Account agency = new Account(
            Name = 'Test Agency',
            RecordTypeId = [
                SELECT Id
                FROM RecordType
                WHERE DeveloperName = 'Agency' AND SObjectType = 'Account'
                LIMIT 1
            ].Id,
            ExternalId__c = 'AGE_12345'
        );
        insert agency;
        system.debug('TEST agency ID: ' + agency.id);

        Account society = new Account(
            Name = 'Test Society',
            RecordTypeId = [
                SELECT Id
                FROM RecordType
                WHERE DeveloperName = 'Society' AND SObjectType = 'Account'
                LIMIT 1
            ].Id,
            ExternalId__c = 'SOC_12345'
        );
        insert society;

        system.debug('TEST society  ID: '+society.id);

        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(
            Name = 'Agenzia',
            FinServ__InverseRole__c = 'Compagnia'
        );
        insert role;

        FinServ__AccountAccountRelation__c mandate = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = agency.Id,
            FinServ__RelatedAccount__c = society.Id,
            FinServ__Role__c = role.Id,
            RecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName()
                .get('AgencySociety')
                .getRecordTypeId()
        );
        insert mandate;

        Account client = new Account(
            Name = 'Test Client',
            RecordTypeId = [
                SELECT Id
                FROM RecordType
                WHERE DeveloperName = 'IndustriesBusiness' AND SObjectType = 'Account'
                LIMIT 1
            ].Id,
            ExternalId__c = 'CLI_12345'
        );
        insert client;
        system.debug('TEST Subject Client ID: '+client.id);


        FinServ__AccountAccountRelation__c aarClientAgency = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = client.Id,
            FinServ__RelatedAccount__c = agency.Id,
            FinServ__Role__c = role.Id,
            RecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName()
                .get('AccountAgency')
                .getRecordTypeId()
        );
        insert aarClientAgency;

        FinServ__AccountAccountRelation__c aarClientSociety = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = client.Id,
            FinServ__RelatedAccount__c = society.Id,
            FinServ__Role__c = role.Id,
            RecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName()
                .get('AccountSociety')
                .getRecordTypeId()
        );
        insert aarClientSociety;

        AccountDetailsNPI__c accountDetailsNPI = new AccountDetailsNPI__c(
            Name = 'Test Account Details NPI',
            ExternalId__c = 'NPI_12345',
            Society__c = society.Id,
            relation__c = aarClientSociety.id
        );
        insert accountDetailsNPI;
        system.debug('TEST AccountDetailsNPI ID: ' + accountDetailsNPI);

        Group groupElement = new Group(
            Name = 'Test Group',
            DeveloperName = 'R_AGE_12345',
            Type = 'Regular'
        );
        insert groupElement;

        //FOR TEST TO REMOVE

        List<FinServ__AccountAccountRelation__c> agencyClientRelations = [
            SELECT FinServ__Account__c, FinServ__RelatedAccount__c
            FROM FinServ__AccountAccountRelation__c
            WHERE RecordType.DeveloperName = 'AccountAgency'
        ];
        System.debug('TEST Agency-Client Relations: ' + agencyClientRelations);


        List<FinServ__AccountAccountRelation__c> agencySocietyRelations = [
            SELECT FinServ__Account__c, FinServ__RelatedAccount__c, FinServ__Account__r.ExternalId__c
            FROM FinServ__AccountAccountRelation__c
            WHERE RecordType.DeveloperName = 'AgencySociety'
        ];
        System.debug('TEST Agency-Society Relations: ' + agencySocietyRelations);

        //FINE FOR TEST TO REMOVE

        // Parametri per il costruttore parametrico
        Set<Id> idsSet = new Set<Id>{accountDetailsNPI.Id};
        Id idValueFrom = accountDetailsNPI.Id;
        Id idValueTo = accountDetailsNPI.Id;
        DateTime dateFrom = DateTime.newInstance(Date.today().year(), Date.today().month(), Date.today().day(), 0, 0, 0);
        DateTime dateTo = DateTime.newInstance(Date.today().year(), Date.today().month(), Date.today().day(), 23, 59, 59);

        // Execute the batch
        Test.startTest();
        AccountDetailsNPIBatch batch = new AccountDetailsNPIBatch(idsSet, idValueFrom, idValueTo, dateFrom, dateTo);
        Database.executeBatch(batch, 200);
        Test.stopTest();

        // Verify results
        List<AccountDetailsNPI__Share> shares = [
            SELECT Id, ParentId, UserOrGroupId, AccessLevel
            FROM AccountDetailsNPI__Share 
            WHERE ParentId = :accountDetailsNPI.Id
            AND AccessLevel='Read'
        ];
        System.assertEquals(1, shares.size(), 'Expected one sharing record to be created.');
        System.assertEquals('Read', shares[0].AccessLevel, 'Expected sharing access level to be Read.');
    }

    @isTest
    static void multiRecordTestBatchExecution() {
        Integer numAgencies = 5;
        Integer numSocieties = 5;
        Integer numClients = 50;

        Id agencyRecordTypeId = [
            SELECT Id
            FROM RecordType
            WHERE DeveloperName = 'Agency' AND SObjectType = 'Account'
            LIMIT 1
        ].Id;

        Id societyRecordTypeId = [
            SELECT Id
            FROM RecordType
            WHERE DeveloperName = 'Society' AND SObjectType = 'Account'
            LIMIT 1
        ].Id;

        Id clientRecordTypeId = [
            SELECT Id
            FROM RecordType
            WHERE DeveloperName = 'IndustriesBusiness' AND SObjectType = 'Account'
            LIMIT 1
        ].Id;

        List<Account> agencies = new List<Account>();
        for (Integer i = 1; i <= numAgencies; i++) {
            agencies.add(new Account(
                Name = 'Test Agency ' + i,
                RecordTypeId = agencyRecordTypeId,
                ExternalId__c = 'AGE_' + i
            ));
        }
        insert agencies;

        List<Account> societies = new List<Account>();
        for (Integer i = 1; i <= numSocieties; i++) {
            societies.add(new Account(
                Name = 'Test Society ' + i,
                RecordTypeId = societyRecordTypeId,
                ExternalId__c = 'SOC_' + i
            ));
        }
        insert societies;

        List<Account> clients = new List<Account>();
        for (Integer i = 1; i <= numClients; i++) {
            clients.add(new Account(
                Name = 'Test Client ' + i,
                RecordTypeId = clientRecordTypeId,
                ExternalId__c = 'CLI_' + i
            ));
        }
        insert clients;

        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(
            Name = 'Agenzia',
            FinServ__InverseRole__c = 'Compagnia'
        );
        insert role;

        List<Group> groups = new List<Group>();
        for (Integer i = 0; i < numAgencies; i++) {
            Group groupElement = new Group(
                Name = 'Test Group ' + i,
                DeveloperName = 'R_' + agencies[i].ExternalId__c,
                Type = 'Regular'
            );
            groups.add(groupElement);
            System.debug('Creating group: ' + groupElement.Name + ' with DeveloperName: ' + groupElement.DeveloperName);           
        }
        insert groups;


        // List<FinServ__AccountAccountRelation__c> mandates = new List<FinServ__AccountAccountRelation__c>();
        // for (Integer i = 0; i < numAgencies; i++) {
        //     Integer maxMandates = (i == 0) ? numSocieties : 2;
        //     for (Integer j = 0; j < maxMandates; j++) {
        //         mandates.add(new FinServ__AccountAccountRelation__c(
        //             FinServ__Account__c = agencies[i].Id,
        //             FinServ__RelatedAccount__c = societies[j].Id,
        //             FinServ__Role__c = role.Id,
        //             RecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName()
        //                 .get('AgencySociety')
        //                 .getRecordTypeId()
        //         ));
        //     }
        // }
        // insert mandates;

        List<FinServ__AccountAccountRelation__c> mandates = new List<FinServ__AccountAccountRelation__c>();
        for (Integer i = 0; i < numAgencies; i++) {            
            for (Integer j = 0; j < numSocieties; j++) {
                mandates.add(new FinServ__AccountAccountRelation__c(
                    FinServ__Account__c = agencies[i].Id,
                    FinServ__RelatedAccount__c = societies[j].Id,
                    FinServ__Role__c = role.Id,
                    RecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName()
                        .get('AgencySociety')
                        .getRecordTypeId()
                ));
            }
        }
        insert mandates;
        System.debug('Inserted ' + mandates.size() + ' FinServ__AccountAccountRelation__c records.');
        System.debug('Mandates: ' + mandates);
        System.debug('Agencies: ' + agencies);
        System.debug('Societies: ' + societies);

        List<FinServ__AccountAccountRelation__c> clientAgencyRelations = new List<FinServ__AccountAccountRelation__c>();
        for (Integer i = 0; i < numClients; i++) {
            clientAgencyRelations.add(new FinServ__AccountAccountRelation__c(
                FinServ__Account__c = clients[i].Id,
                FinServ__RelatedAccount__c = agencies[Math.Mod(i, numAgencies)].Id,
                FinServ__Role__c = role.Id,
                RecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName()
                    .get('AccountAgency')
                    .getRecordTypeId()
            ));
        }
        insert clientAgencyRelations;

        List<FinServ__AccountAccountRelation__c> clientSocietyRelations = new List<FinServ__AccountAccountRelation__c>();
        for (Integer i = 0; i < numClients; i++) {
            for (Integer j = 0; j < numSocieties; j++) {
                clientSocietyRelations.add(new FinServ__AccountAccountRelation__c(
                    FinServ__Account__c = clients[i].Id,
                    FinServ__RelatedAccount__c = societies[j].Id,
                    FinServ__Role__c = role.Id,
                    RecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName()
                        .get('AccountSociety')
                        .getRecordTypeId()
                ));
            }
        }
        insert clientSocietyRelations;

        // Prepara dati per costruttore parametrico
        List<AccountDetailsNPI__c> npiRecords = new List<AccountDetailsNPI__c>();

            for (Integer i = 0; i < mandates.size(); i++) {
                npiRecords.add(new AccountDetailsNPI__c(
                    Name = 'Test Account Details NPI ' + i,
                    ExternalId__c = 'NPI_' + i + '_AGENCY_' + mandates[i].FinServ__Account__r.ExternalId__c,
                    Society__c = mandates[i].FinServ__RelatedAccount__c,
                    relation__c = mandates[i].Id
                ));
            }
        insert npiRecords;

        System.debug('Inserted AccountDetailsNPI__c records: ' + npiRecords);

        Set<Id> idsSet = new Set<Id>();
        for (AccountDetailsNPI__c npi : npiRecords) {
            idsSet.add(npi.Id);
        }
        Id idValueFrom = npiRecords[0].Id;
        Id idValueTo = npiRecords[npiRecords.size() - 1].Id;
        DateTime dateFrom = DateTime.newInstance(Date.today().year(), Date.today().month(), Date.today().day(), 0, 0, 0);
        DateTime dateTo = DateTime.newInstance(Date.today().year(), Date.today().month(), Date.today().day(), 23, 59, 59);

        // Execute the batch
        System.debug('Starting batch execution with ' + idsSet.size() + ' records.');
        System.debug('ID Range: ' + idValueFrom + ' to ' + idValueTo);
        System.debug('Date Range: ' + dateFrom + ' to ' + dateTo);
        System.debug('Number of records to process: ' + npiRecords.size());
        System.debug('Executing batch with parameters: ' + idsSet + ', ' + idValueFrom + ', ' + idValueTo + ', ' + dateFrom + ', ' + dateTo);
        // Execute the batch
        System.debug('Executing batch...');
        Test.startTest();
        AccountDetailsNPIBatch batch = new AccountDetailsNPIBatch(idsSet, idValueFrom, idValueTo, dateFrom, dateTo);
        Id batchJobId = Database.executeBatch(batch, 50);
        Test.stopTest();

        AsyncApexJob batchJob = [
            SELECT Id, Status, NumberOfErrors, JobItemsProcessed, TotalJobItems
            FROM AsyncApexJob
            WHERE Id = :batchJobId
        ];

        // if (batchJob.Status == 'Completed') {
        //     List<AccountDetailsNPI__Share> shares = [
        //         SELECT Id, ParentId, UserOrGroupId, AccessLevel
        //         FROM AccountDetailsNPI__Share
        //         WHERE AccessLevel = 'Read'
        //     ];
        //     System.assert(shares.size() > 0, 'Expected sharing records to be created.');
        // } else {
        //     System.debug('Batch did not complete successfully. Status: ' + batchJob.Status);
        // }
        List<AccountDetailsNPI__Share> shares = [
                SELECT Id, ParentId, UserOrGroupId, AccessLevel
                FROM AccountDetailsNPI__Share
                WHERE AccessLevel = 'Read'
            ];

        System.assert(shares.size() > 0, 'Expected sharing records to be created.');
        
    }

    @isTest
    static void testBatchConstructorWithStringDate() {
        String dateStr = String.valueOf(Date.today());
        Test.startTest();
        AccountDetailsNPIBatch batch = new AccountDetailsNPIBatch(dateStr);
        Test.stopTest();
        // Assert: la variabile dt deve essere valorizzata
        System.assertNotEquals(null, batch.dt, 'dt should not be null for valid string date');
        System.assertEquals(Date.today(), batch.dt, 'dt should match the input date');
    }

     @isTest
    static void testBatchConstructorWithDateObject() {
        Date dateObj = Date.today();
        Test.startTest();
        AccountDetailsNPIBatch batch = new AccountDetailsNPIBatch(dateObj);
        Test.stopTest();
        // Assert: la variabile dt deve essere valorizzata
        System.assertNotEquals(null, batch.dt, 'dt should not be null for valid Date object');
        System.assertEquals(Date.today(), batch.dt, 'dt should match the input date');
    }
}