<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>GetFEISettings</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetFEISettingsCustom8380</globalKey>
        <inputFieldName>JWT_scope__c</inputFieldName>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetFEISettingsCustom873</globalKey>
        <inputFieldName>Gateway_Endpoint__c</inputFieldName>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetFEISettingsCustom3076</globalKey>
        <inputFieldName>Extended_CONTID__c</inputFieldName>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetFEISettingsCustom9451</globalKey>
        <inputFieldName>CONTID_Parameter_Name__c</inputFieldName>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterDataType>BOOLEAN</filterDataType>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&quot;true&quot;</filterValue>
        <globalKey>GetFEISettingsCustom4713</globalKey>
        <inputFieldName>Active__c</inputFieldName>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetFEISettingsCustom6473</globalKey>
        <inputFieldName>Environment__c</inputFieldName>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetFEISettingsCustom6931</globalKey>
        <inputFieldName>sendFEIRequest_Payload__c</inputFieldName>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>GetFEISettingsCustom5632</globalKey>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetFEISettingsCustom6007</globalKey>
        <inputFieldName>Label</inputFieldName>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetFEISettingsCustom8843</globalKey>
        <inputFieldName>FEIAddress_Hostpoint__c</inputFieldName>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetFEISettingsCustom1260</globalKey>
        <inputFieldName>FEI_Type__c</inputFieldName>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetFEISettingsCustom1814</globalKey>
        <inputFieldName>Active__c</inputFieldName>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetFEISettingsCustom8456</globalKey>
        <inputFieldName>UCA_Permission_Name__c</inputFieldName>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterDataType>STRING</filterDataType>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>FEIID</filterValue>
        <globalKey>GetFEISettingsCustom3405</globalKey>
        <inputFieldName>Label</inputFieldName>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetFEISettingsCustom2978</globalKey>
        <inputFieldName>FEIAddress_Endpoint__c</inputFieldName>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetFEISettingsCustom3990</globalKey>
        <inputFieldName>Landing_URL__c</inputFieldName>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterDataType>TEXTAREA</filterDataType>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Environment</filterValue>
        <globalKey>GetFEISettingsCustom9912</globalKey>
        <inputFieldName>Environment__c</inputFieldName>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetFEISettingsCustom5985</globalKey>
        <inputFieldName>JWT_requireDomain__c</inputFieldName>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetFEISettingsCustom2858</globalKey>
        <inputFieldName>FEIAddress_Identity__c</inputFieldName>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetFEISettingsCustom2099</globalKey>
        <inputFieldName>BackURL__c</inputFieldName>
        <inputObjectName>FEI_Settings__mdt</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetFEISettings</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>FEI_Settings__mdt</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;Environment&quot; : &quot;SVIL&quot;,
  &quot;FEIID&quot; : &quot;VITA.POLIZZA&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Turbo Extract</type>
    <uniqueName>GetFEISettings_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
