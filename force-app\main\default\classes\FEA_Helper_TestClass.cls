@isTest
 with sharing class FEA_Helper_TestClass {
    @testSetup
    static void setup() {

        // Create RecordTypes for Agency and Society
        Id agencyRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId();
        Id societyRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId();
        Id passportRecordTypeId = Schema.SObjectType.ContentVersion.getRecordTypeInfosByDeveloperName().get('PAS').getRecordTypeId();

        // Creazione Account
        List<Account> accListToInsert = new List<Account>();
        Account accountCliente1 = new Account(
            FirstName = 'Cliente', LastName = '1',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('PersonAccount').getRecordTypeId(), 
            ExternalId__c = 'Cliente1'
        );
        accListToInsert.add(accountCliente1);

        Account accountCliente2 = new Account(
            FirstName = 'Cliente', LastName = '2',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('PersonAccount').getRecordTypeId(), 
            ExternalId__c = 'Cliente2'
        );
        accListToInsert.add(accountCliente2);

        Account accountCliente3 = new Account(
            FirstName = 'Cliente', LastName = '3',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('PersonAccount').getRecordTypeId(), 
            ExternalId__c = 'Cliente3'
        );
        accListToInsert.add(accountCliente3);

        Account accountCliente4 = new Account(
            FirstName = 'Cliente', LastName = '4',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('PersonAccount').getRecordTypeId(), 
            ExternalId__c = 'Cliente4'
        );
        accListToInsert.add(accountCliente4);

        Account accountSociety1 = new Account(
            Name = 'UNIPOL', RecordTypeId = societyRecordTypeId,  
            ExternalId__c = 'SOC_1', Identifier__c = '1'
        );
        accListToInsert.add(accountSociety1);

        Account accountAgenzia1 = new Account(
            Name = 'Agenzia 1', RecordTypeId = agencyRecordTypeId,  
            ExternalId__c = 'AGE_1'
        );
        accListToInsert.add(accountAgenzia1);
        insert accListToInsert;
        System.debug('@GP accListToInsert: ' + accListToInsert);

        // Creazione ReciprocalRole
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Agenzia');
        insert role;
       
        FinServ__ReciprocalRole__c roleC = new FinServ__ReciprocalRole__c(Name = 'Agenzia', FinServ__InverseRole__c = 'Compagnia');
        insert roleC;

        Profile p = [SELECT Id FROM Profile WHERE Name = 'System Administrator' LIMIT 1];
        User u = new User(
            Alias = 'testu',
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8',
            IdAzienda__c = accountAgenzia1.Id,
            LastName = 'Test',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            ProfileId = p.Id,
            TimeZoneSidKey = 'America/Los_Angeles',
            FederationIdentifier = 'PRLGLR95',
            Username = 'testuser' + DateTime.now().getTime() + '@example.com'
        );
        insert u;

        NetworkUser__c nu = new NetworkUser__c();
        nu.NetworkUser__c = '1X384X0';
        nu.FiscalCode__c = 'PRLGLR95';
        nu.IsActive__c = true;
        nu.Profile__c = 'A';
        nu.Agency__c = accountAgenzia1.Id;
        nu.Society__c = 'SOC_1';
        insert nu;

        // Creazione degli AccountAccountRel
        List<FinServ__AccountAccountRelation__c> accAccRelListToInsert = new List<FinServ__AccountAccountRelation__c>();
        FinServ__AccountAccountRelation__c accAccRelClienteSociety1 = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = accountCliente1.Id,
            FinServ__RelatedAccount__c = accountSociety1.Id,
            FinServ__Role__c = role.Id
            //RecordTypeId = AccountSocietyRecordTypeId
        );
        accAccRelListToInsert.add(accAccRelClienteSociety1);

        FinServ__AccountAccountRelation__c accAccRelClienteSociety2 = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = accountCliente2.Id,
            FinServ__RelatedAccount__c = accountSociety1.Id,
            FinServ__Role__c = role.Id
            //RecordTypeId = AccountSocietyRecordTypeId
        );
        accAccRelListToInsert.add(accAccRelClienteSociety2);

        FinServ__AccountAccountRelation__c accAccRelClienteSociety3 = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = accountCliente3.Id,
            FinServ__RelatedAccount__c = accountSociety1.Id,
            FinServ__Role__c = role.Id
            //RecordTypeId = AccountSocietyRecordTypeId
        );
        accAccRelListToInsert.add(accAccRelClienteSociety3);

        FinServ__AccountAccountRelation__c accAccRelClienteSociety4 = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = accountCliente4.Id,
            FinServ__RelatedAccount__c = accountSociety1.Id,
            FinServ__Role__c = role.Id
            //RecordTypeId = AccountSocietyRecordTypeId
        );
        accAccRelListToInsert.add(accAccRelClienteSociety4);

        FinServ__AccountAccountRelation__c accAccRelClienteAgency = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = accountCliente1.Id,
            FinServ__RelatedAccount__c = accountAgenzia1.Id,
            FinServ__Role__c = role.Id
            //RecordTypeId = AccountAgencyRecordTypeId
        );
        accAccRelListToInsert.add(accAccRelClienteAgency);
        insert accAccRelListToInsert;
        
        //Creazione  AccountDetails
        List<AccountDetails__c> accDetListToInsert = new List<AccountDetails__c>();
        AccountDetails__c accDetails1 = new AccountDetails__c(
        	Relation__c = accAccRelClienteSociety1.Id, SourceSystemIdentifier__c = '000001', 
            FeaMail__c = '<EMAIL>', FeaMobile__c = '+39 **********',
            Country__c = 'ITA', FiscalCode__c = '****************', Street__c = 'Via del Campo',
            PostalCode__c = '20900', City__c = 'Firenze', State__c = 'TO', Mobile__c = '+39 *********',
            Email__c = '<EMAIL>'
        );
        accDetListToInsert.add(accDetails1);

        AccountDetails__c accDetails2 = new AccountDetails__c(
        	Relation__c = accAccRelClienteSociety2.Id, SourceSystemIdentifier__c = '000002', 
            FeaMail__c = '<EMAIL>', FeaMobile__c = '+39 **********',
            Country__c = 'ITA' 
        );
        accDetListToInsert.add(accDetails2);

        AccountDetails__c accDetails3 = new AccountDetails__c(
        	Relation__c = accAccRelClienteSociety3.Id, SourceSystemIdentifier__c = '000003', 
            FeaMail__c = '<EMAIL>', FeaMobile__c = '+39 **********',
            Country__c = 'ITA', SourceSystemConsentCode__c = '05', FeaVersionType__c = '0' 
        );
        accDetListToInsert.add(accDetails3);

        AccountDetails__c accDetails4 = new AccountDetails__c(
        	Relation__c = accAccRelClienteSociety4.Id, SourceSystemIdentifier__c = '000004', 
            FeaMail__c = '<EMAIL>', FeaMobile__c = '+39 **********',
            Country__c = 'ITA', FeaVersionType__c = '2', FeaEffectiveDate__c = Date.today().addMonths(-1),
            FiscalCode__c = '****************'
        );
        accDetListToInsert.add(accDetails4);

        AccountDetails__c accDetails5 = new AccountDetails__c(
        	Relation__c = accAccRelClienteSociety4.Id, SourceSystemIdentifier__c = '000005', 
            FeaMail__c = '<EMAIL>', FeaMobile__c = '+39 **********',
            Country__c = 'ITA', FeaVersionType__c = '2', FeaEffectiveDate__c = Date.today().addMonths(-1),
            FiscalCode__c = 'PRLGRL8807F788E', EmailStatus__c = 'Certified'
        );
        accDetListToInsert.add(accDetails5);
        insert accDetListToInsert;

        // Step 1: Carica il file come ContentVersion
        ContentVersion cv = new ContentVersion();
        cv.Title = 'Passaporto';
        cv.PathOnClient = 'Passaporto.pdf';
        cv.VersionData = Blob.valueOf('Contenuto del file in formato testo o base64');
        cv.ExpirationDate__c = Date.today().addDays(1);
        cv.RecordTypeId = passportRecordTypeId;
        insert cv;

        // Step 2: Recupera l'ID del ContentDocument
        cv = [SELECT Id, ContentDocumentId FROM ContentVersion WHERE Id = :cv.Id];

        // Step 3: Collega il documento all'Account
        ContentDocumentLink cdl = new ContentDocumentLink();
        cdl.ContentDocumentId = cv.ContentDocumentId;
        cdl.LinkedEntityId = accDetails1.Id; // ID dell'AccountDetails
        cdl.ShareType = 'V'; // V = Viewer, C = Collaborator, I = Inferred
        cdl.Visibility = 'AllUsers';
        insert cdl;
        
        FEI_Environment__c setting = new FEI_Environment__c(Environment__c = 'EURO', SetupOwnerId = UserInfo.getOrganizationId());
        insert setting;

    }

    @isTest
    static void checkDatiUnivociScenarioOK() {
        Map<String,Object> inputMap = new Map<String,Object>();
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();

        inputMap.put('ciu','000001');
        inputMap.put('email','<EMAIL>');
        inputMap.put('cellulare','+39 **********');

        FEA_Helper.checkDatiUnivoci(inputMap, outputMap, optionsMap);
    }   

    @isTest
    static void checkDatiUnivociScenarioKODuplicati() {
        Map<String,Object> inputMap = new Map<String,Object>();
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();

        inputMap.put('ciu','000002');
        inputMap.put('email','<EMAIL>');
        inputMap.put('cellulare','+39 **********');

        FEA_Helper.checkDatiUnivoci(inputMap, outputMap, optionsMap);
    }   

    @isTest
    static void checkDatiUnivociScenarioKOCiu() {
        Map<String,Object> args = new Map<String,Object>();
        Map<String,Object> inputMap = new Map<String,Object>();
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();

        inputMap.put('ciu','000012');
        inputMap.put('email','<EMAIL>');
        inputMap.put('cellulare','+39 **********');
        args.put('input',inputMap);
        args.put('output',outputMap);
        args.put('options',optionsMap);

        FEA_Helper fH = new FEA_Helper();
        fH.call('checkDatiUnivoci', args);
    }   

    @isTest
    static void checkActiveDocumentOK() {
        Map<String,Object> args = new Map<String,Object>();
        Map<String,Object> inputMap = new Map<String,Object>();
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();

        inputMap.put('ciu','000001');
        args.put('input',inputMap);
        args.put('output',outputMap);
        args.put('options',optionsMap);

        FEA_Helper fH = new FEA_Helper();
        fH.call('checkActiveDocument', args);
    }  
    
    @isTest
    static void checkActiveDocumentKO() {
        Map<String,Object> args = new Map<String,Object>();
        Map<String,Object> inputMap = new Map<String,Object>();
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();

        inputMap.put('ciu','000012');
        args.put('input',inputMap);
        args.put('output',outputMap);
        args.put('options',optionsMap);

        FEA_Helper fH = new FEA_Helper();
        fH.call('checkActiveDocument', args);
    }

    @isTest
    static void createBarcodeAndXMLOK() {
        Map<String,Object> args = new Map<String,Object>();
        Map<String,Object> inputMap = new Map<String,Object>();
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();

        inputMap.put('ciu','000001');
        inputMap.put('type','FEA');

        args.put('input',inputMap);
        args.put('output',outputMap);
        args.put('options',optionsMap);
        
        User u = [SELECT Id FROM User WHERE Alias = 'testu' LIMIT 1];
        System.runAs(u) {
            FEA_Helper fH = new FEA_Helper();
            try{
                fH.call('createBarcodeAndXML', args);
            }catch(Exception ex){}
        } 
    }

    @isTest
    static void createBarcodeAndXMLFESOK() {
        Map<String,Object> args = new Map<String,Object>();
        Map<String,Object> inputMap = new Map<String,Object>();
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();

        inputMap.put('ciu','000003');
        inputMap.put('type','FES');

        args.put('input',inputMap);
        args.put('output',outputMap);
        args.put('options',optionsMap);
        User u = [SELECT Id FROM User WHERE Alias = 'testu' LIMIT 1];
        System.runAs(u) {
            FEA_Helper fH = new FEA_Helper();
            try{
                fH.call('createBarcodeAndXML', args);
            }catch(Exception ex){}
        }
    }

    @isTest
    static void createRevocaRequestOK() {
        Map<String,Object> args = new Map<String,Object>();
        Map<String,Object> inputMap = new Map<String,Object>();
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();

        inputMap.put('ciu','000004');
        inputMap.put('feaId','23456789876543');
        inputMap.put('compagnia','unipolsai');
        inputMap.put('idContattoFea','34567876');
        inputMap.put('email','<EMAIL>');
        inputMap.put('cellulare','+39 6787678');
        inputMap.put('statoValidazioneMail','V');
        
        args.put('input',inputMap);
        args.put('output',outputMap);
        args.put('options',optionsMap);
        try{
        FEA_Helper fH = new FEA_Helper();
        fH.call('revocaFEA', args);
        }catch (Exception e) {
            System.assert(true, 'Gestione eccezione');
        }
    }

    @isTest
    static void certifcazioneContattiOK() {
        Map<String,Object> args = new Map<String,Object>();
        Map<String,Object> inputMap = new Map<String,Object>();
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();

        inputMap.put('ciu','000005');
        inputMap.put('email','<EMAIL>');
        inputMap.put('cellulare','45678456');
        
        args.put('input',inputMap);
        args.put('output',outputMap);
        args.put('options',optionsMap);

        FEA_Helper fH = new FEA_Helper();
        fH.call('certifcazioneContatti', args);
    }

    @isTest
    static void checkAggiornaOK() {
        Map<String,Object> args = new Map<String,Object>();
        Map<String,Object> inputMap = new Map<String,Object>();
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();

        inputMap.put('ciu','000004');
        
        args.put('input',inputMap);
        args.put('output',outputMap);
        args.put('options',optionsMap);

        FEA_Helper fH = new FEA_Helper();
        fH.call('checkAggiorna', args);
    }

    @isTest
    static void refreshStatoEmailOK() {
        Map<String,Object> args = new Map<String,Object>();
        Map<String,Object> inputMap = new Map<String,Object>();
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();

        inputMap.put('ciu','000005');
        
        args.put('input',inputMap);
        args.put('output',outputMap);
        args.put('options',optionsMap);

        FEA_Helper fH = new FEA_Helper();
        fH.call('refreshStatoEmail', args);
    }

    @isTest
    static void getDataOdiernaOK() {
        Map<String,Object> args = new Map<String,Object>();
        Map<String,Object> inputMap = new Map<String,Object>();
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();
        
        args.put('input',inputMap);
        args.put('output',outputMap);
        args.put('options',optionsMap);

        FEA_Helper fH = new FEA_Helper();
        fH.call('getDataOdierna', args);
    }

    @isTest
    static void getCompanyNameOK() {
        Map<String,Object> args = new Map<String,Object>();
        Map<String,Object> inputMap = new Map<String,Object>();
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();
        
        inputMap.put('company', 'UNIPOL');
        args.put('input',inputMap);
        args.put('output',outputMap);
        args.put('options',optionsMap);

        FEA_Helper fH = new FEA_Helper();
        fH.call('getCompanyName', args);
    }

    @isTest
    static void getUserFiscalCodeOK() {
        Map<String,Object> args = new Map<String,Object>();
        Map<String,Object> inputMap = new Map<String,Object>();
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();
        
        args.put('input',inputMap);
        args.put('output',outputMap);
        args.put('options',optionsMap);

        FEA_Helper fH = new FEA_Helper();
        fH.call('getUserFiscalCode', args);
    }

}