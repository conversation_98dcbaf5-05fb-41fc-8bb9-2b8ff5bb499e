public without sharing class CaseRulesAssignementBI {

    public static void manageCaseGroupsOwnership(List<Case> newListCase) {
        PolicyKey.CaseClassificationResult result = classifyCases(newListCase);

        Set<String> cipSet = result.cipSet;
        Set<String> fiscalCodes = result.fiscalCodes;
        List<Case> r01r07Cases = result.r01r07Cases;
        List<Case> r02r08Cases = result.r02r08Cases;
        List<Case> casesWithPolicy = result.casesWithPolicy;

        Map<String, Id> cipToGroupId = cipSet.isEmpty() ? new Map<String, Id>() : fetchGroupCip(cipSet);
        system.debug('%%cipToGroupId ' + cipToGroupId);
        Map<String, User> userMap = fiscalCodes.isEmpty() ? new Map<String, User>() : getUserByComponent(fiscalCodes);
        system.debug('%%userMap ' + userMap);
        system.debug('%%casesWithPolicy ' + casesWithPolicy);

        Map<String, InsurancePolicy> policyMap = new Map<String, InsurancePolicy>();
        if (!casesWithPolicy.isEmpty()) {
            policyMap = fetchPoliciesByBusinessKey(casesWithPolicy);
            populateInsurancePolicyField(casesWithPolicy, policyMap);
        }
        system.debug('%%policyMap ' + policyMap);

        PolicyKey.AssignmentRuleMaps ruleMaps = prepareAssignmentRuleMaps(r01r07Cases, r02r08Cases, policyMap);
        System.debug('%%ruleMaps ' + ruleMaps);

        PolicyKey.AssignmentContextData ctx = new PolicyKey.AssignmentContextData(cipToGroupId, userMap, ruleMaps);
        System.debug('%%ctx ' + ctx);

        assignCases(newListCase, ctx);
    }

    private static PolicyKey.CaseClassificationResult classifyCases(List<Case> newListCase) {
        PolicyKey.CaseClassificationResult result = new PolicyKey.CaseClassificationResult();

        for (Case c : newListCase) {
            classifyDrAssegnazione(c, result);
            classifyAssignmentRule(c, result);
            classifyPolicyCase(c, result);
        }

        return result;
    }

    private static void classifyDrAssegnazione(Case c, PolicyKey.CaseClassificationResult result) {
        if (c.DrAssegnazione__c == '1' && c.DrAssegnatario__c != null && c.DrAgenziaFiglia__c != null) {
            result.cipSet.add('CIP_SOC_' + c.DrAssegnazione__c + '_AGE_' + c.DrAgenziaFiglia__c + '_' + c.DrAssegnatario__c);
        } else if (c.DrAssegnazione__c == '3' && !String.isBlank(c.DrAssegnatario__c)) {
            result.fiscalCodes.add(c.DrAssegnatario__c);
        }
    }

    private static void classifyAssignmentRule(Case c, PolicyKey.CaseClassificationResult result) {
        if (c.TECH_AssignmentRules__c == 'R01' || c.TECH_AssignmentRules__c == 'R07') {
            result.r01r07Cases.add(c);
        } else if (c.TECH_AssignmentRules__c == 'R02' || c.TECH_AssignmentRules__c == 'R08') {
            if (!'CLAIM'.equalsIgnoreCase(c.DrContextEntity__c) &&
            !'LIQUIDO'.equalsIgnoreCase(c.DrCanale__c)) {
                result.r02r08Cases.add(c);
            }
        }
    }

    private static void classifyPolicyCase(Case c, PolicyKey.CaseClassificationResult result) {
        if (!String.isBlank(c.BusinessKey__c) && !'CLAIM'.equalsIgnoreCase(c.DrContextEntity__c)
            && (c.DrCanale__c == null || !'LIQUIDO'.equalsIgnoreCase(c.DrCanale__c))) {
                result.casesWithPolicy.add(c);
        }
    }

    private static PolicyKey.AssignmentRuleMaps prepareAssignmentRuleMaps(List<Case> r01r07Cases,List<Case> r02r08Cases,Map<String, InsurancePolicy> policyMap) {
        Map<String, String> cipMapR01R07 = new Map<String, String>();
        Map<String, Id> cipToGroupIdR01R07 = new Map<String, Id>();
        if (!r01r07Cases.isEmpty()) {
            cipMapR01R07 = resolveR01R07(r01r07Cases);
            cipToGroupIdR01R07 = fetchGroupCip(new Set<String>(cipMapR01R07.values()));
        }

        Map<String, String> cipMapR02R08 = new Map<String, String>();
        Map<String, Id> cipToGroupIdR02R08 = new Map<String, Id>();
        if (!r02r08Cases.isEmpty()) {
            cipMapR02R08 = resolveR02R08(r02r08Cases, policyMap);
            cipToGroupIdR02R08 = fetchGroupCip(new Set<String>(cipMapR02R08.values()));
        }

        return new PolicyKey.AssignmentRuleMaps(cipMapR01R07, cipToGroupIdR01R07, cipMapR02R08, cipToGroupIdR02R08);
    }

    private static void assignCases(List<Case> newListCase, PolicyKey.AssignmentContextData ctx) {
        for (Case c : newListCase) {
            system.debug('%%c.DrAssegnazione__c ' + c.DrAssegnazione__c);
            System.debug('%%ctx ' + ctx);
            Boolean assigned = false;
            if (c.DrAssegnazione__c == '1') {
                assigned = assignType1(c, ctx);
            } else if (c.DrAssegnazione__c == '2') {
                assigned = assignByAssignmentRules(c, ctx.ruleMaps);
            } else if (c.DrAssegnazione__c == '3') {
                assigned = assignType3(c, ctx);
            }
        }
    }

    private static Boolean assignType1(Case c, PolicyKey.AssignmentContextData ctx) {
        String cip = 'CIP_SOC_' + c.DrAssegnazione__c + '_AGE_' + c.DrAgenziaFiglia__c + '_' + c.DrAssegnatario__c;
        Id groupId = ctx.cipToGroupId.get(cip);
        if (groupId != null) {
            c.AssignedGroup__c = groupId;
            return true;
        }
        return assignByAssignmentRules(c, ctx.ruleMaps);
    }

    private static Boolean assignType3(Case c, PolicyKey.AssignmentContextData ctx) {
        User u = ctx.userMap.get(c.DrAssegnatario__c);
        System.debug('%%userMap: ' + ctx.userMap);
        if (u != null) {
            c.AssignedTo__c = u.Id;
            return true;
        }
        System.debug('%%ruleMaps: ' + ctx.ruleMaps);
        return assignByAssignmentRules(c, ctx.ruleMaps);
    }

    private static Boolean assignByAssignmentRules(Case c, PolicyKey.AssignmentRuleMaps maps) {
        String rule = c.TECH_AssignmentRules__c;
        System.debug('%%maps: ' + maps.cipMapR01R07);
        System.debug('%%maps: ' + maps.cipToGroupIdR01R07);
        System.debug('%%maps: ' + maps.cipMapR02R08);
        System.debug('%%maps: ' + maps.cipToGroupIdR02R08);
        if (rule == 'R01' || rule == 'R07' && (!maps.cipMapR01R07.isEmpty() && !maps.cipToGroupIdR01R07.isEmpty())) {
            PolicyKey.AssignmentContext ctx = new PolicyKey.AssignmentContext(c, maps.cipMapR01R07, maps.cipToGroupIdR01R07);
            return assignFromAccountAgency(ctx);
        }
        if (rule == 'R02' || rule == 'R08' && (!maps.cipMapR02R08.isEmpty() && !maps.cipToGroupIdR02R08.isEmpty())) {
            PolicyKey.AssignmentContext ctx = new PolicyKey.AssignmentContext(c, maps.cipMapR02R08, maps.cipToGroupIdR02R08);
            return assignFromBusinessKey(ctx);
        }
        return false;
    }

    private static Boolean assignFromAccountAgency(PolicyKey.AssignmentContext ctx) {
        System.debug('%%ctx.c.AccountId ' + ctx.c.AccountId);
        System.debug('%%ctx.c.Agency__c ' + ctx.c.Agency__c);
        System.debug('%%ctx.c.LeoActivityCode__c ' + ctx.c.LeoActivityCode__c);
        System.debug('%%ctx.cipMap ' + ctx.cipMap);
        if (ctx.c.AccountId == null || ctx.c.Agency__c == null || ctx.c.LeoActivityCode__c == null) {
            return false;
        }
        String key = ctx.c.LeoActivityCode__c + '-' + ctx.c.AccountId + '-' + ctx.c.Agency__c;
        String cip = ctx.cipMap.get(key);
        if (cip == null) {
            return false;
        }
        system.debug('%%cip ' + cip);
        system.debug('%%ctx.cipToGroupId ' + ctx.cipToGroupId);
        Id groupId = ctx.cipToGroupId.get(cip);
        system.debug('%%groupId ' + groupId);
        if (groupId == null) {
            return false;
        }
        ctx.c.AssignedGroup__c = groupId;
        return true;
    }

    private static Boolean assignFromBusinessKey(PolicyKey.AssignmentContext ctx) {
        if ('CLAIM'.equalsIgnoreCase(ctx.c.DrContextEntity__c) || 'LIQUIDO'.equalsIgnoreCase(ctx.c.DrCanale__c)) {
            return false;
    	}
        system.debug('%%ctx.c.BusinessKey__c ' + ctx.c.BusinessKey__c);
        system.debug('%%ctx.c.LeoActivityCode__c ' + ctx.c.LeoActivityCode__c);
        system.debug('%%ctx.cipMap ' + ctx.cipMap);
        if (ctx.c.BusinessKey__c == null || ctx.c.LeoActivityCode__c == null) {
            return false;
        }
        PolicyKey pk = PolicyKey.fromBusinessKey(ctx.c.BusinessKey__c, ctx.c.LeoActivityCode__c);
        if (pk == null) {
            return false;
        }
        String cip = ctx.cipMap.get(pk.getReturnKey());
        if (cip == null) {
            return false;
        }
        Id groupId = ctx.cipToGroupId.get(cip);
        if (groupId == null) {
            return false;
        }
        ctx.c.AssignedGroup__c = groupId;
        return true;
    }

	private static Map<String, String> resolveR01R07(List<Case> r01r07Cases) {
        Map<String, String> result = new Map<String, String>();
        Map<String, Case> keyToCase = new Map<String, Case>();
        Set<Id> accountIds = new Set<Id>();
        Set<Id> agencyIds = new Set<Id>();

        for (Case c : r01r07Cases) {
            if (c.AccountId != null && c.Agency__c != null && c.LeoActivityCode__c != null) {
                String accAgeKey = c.AccountId + '-' + c.Agency__c;
                keyToCase.put(accAgeKey, c);
                accountIds.add(c.AccountId);
                agencyIds.add(c.Agency__c);
            }
        }
        system.debug('%%keyToCase ' + keyToCase);
        system.debug('%%accountIds ' + accountIds);
        system.debug('%%agencyIds ' + agencyIds);

        if (keyToCase.isEmpty()) { return result; }

        if (!Schema.sObjectType.FinServ__AccountAccountRelation__c.isAccessible() || !Schema.sObjectType.AccountAgencyDetails__c.isAccessible()) {
            return result;
        }

        Map<String, Id> accAgeKeyToRelId = new Map<String, Id>();
        for (FinServ__AccountAccountRelation__c rel : [SELECT Id, FinServ__Account__c, FinServ__RelatedAccount__c FROM FinServ__AccountAccountRelation__c
            WHERE FinServ__Account__c IN :accountIds AND FinServ__RelatedAccount__c IN :agencyIds]) {
            accAgeKeyToRelId.put(rel.FinServ__Account__c + '-' + rel.FinServ__RelatedAccount__c, rel.Id);
        }
        system.debug('%%accAgeKeyToRelId ' + accAgeKeyToRelId);

        Map<Id, String> relIdToSubAgency = new Map<Id, String>();
        for (AccountAgencyDetails__c aad : [SELECT SubAgencyCode__c, Relation__c FROM AccountAgencyDetails__c WHERE Relation__c IN :accAgeKeyToRelId.values()]) {
            relIdToSubAgency.put(aad.Relation__c, aad.SubAgencyCode__c);
        }
        system.debug('%%relIdToSubAgency ' + relIdToSubAgency);

        for (String accAgeKey : keyToCase.keySet()) {
            Id relId = accAgeKeyToRelId.get(accAgeKey);
            if (relId != null && relIdToSubAgency.containsKey(relId)) {
                Case c = keyToCase.get(accAgeKey);
                String leo = c.LeoActivityCode__c;
                String cip = 'CIP_SOC_' + c.DrAssegnazione__c + '_AGE_' + c.DrAgenziaFiglia__c + '_' + relIdToSubAgency.get(relId);
                result.put(leo + '-' + accAgeKey, cip);
            }
        }

        return result;
    }
	
	private static Map<String, String> resolveR02R08(List<Case> r02r08Cases, Map<String, InsurancePolicy> policyMap) {
        Map<String, String> result = new Map<String, String>();
        for (Case c : r02r08Cases) {
            if (c.BusinessKey__c == null || c.LeoActivityCode__c == null) {
                continue;
            }
            PolicyKey pk = PolicyKey.fromBusinessKey(c.BusinessKey__c, c.LeoActivityCode__c);
            if (pk == null) {
                continue;
            }
            InsurancePolicy policy = policyMap.get(pk.businessKey);
            if (policy != null && policy.CIP__c != null) {
                String valueCip = 'CIP_SOC_' + policy.CompanyCode__c + '_AGE_' + c.DrAgenziaFiglia__c + '_00' + policy.CIP__c;
                result.put(pk.getReturnKey(), valueCip);
            }
        }
        return result;
    }
	
	private static Map<String, InsurancePolicy> fetchPoliciesByBusinessKey(List<Case> cases) {
        List<PolicyKey> policyKeys = new List<PolicyKey>();
        Set<Id> accountIds = new Set<Id>();
        for (Case c : cases) {
            PolicyKey pk = PolicyKey.fromBusinessKey(c.BusinessKey__c, c.LeoActivityCode__c);
            if (pk != null) {
                policyKeys.add(pk);
            }
            if (c.AccountId != null) {
                accountIds.add(c.AccountId);
            }
        }

        if (policyKeys.isEmpty() && accountIds.isEmpty()) {
            return new Map<String, InsurancePolicy>();
        }

        List<String> conditions = new List<String>();
        for (PolicyKey pk : policyKeys) {
            conditions.add(pk.toSoqlCondition());
        }

        String query = 'SELECT Id, CIP__c, CompanyCode__c, MotherAgencyCode__c, AgencyCode__c, PolicyBranchCode__c, ReferencePolicyNumber, NameInsuredId,RecordType.DeveloperName ' +
                   'FROM InsurancePolicy WHERE NameInsuredId IN :accountIds AND (' + String.join(conditions, ' OR ') + ')';
        System.debug('%%query '+ query);
        List<InsurancePolicy> policies = Database.query(query);

        Map<String, InsurancePolicy> result = new Map<String, InsurancePolicy>();
        for (InsurancePolicy policy : policies) {
            for (PolicyKey pk : policyKeys) {
                if (pk.matches(policy)) {
                    result.put(pk.businessKey, policy);
                    break;
                }
            }
        }

        return result;
    }

    private static void populateInsurancePolicyField(List<Case> cases, Map<String, InsurancePolicy> policyMap) {
        if (policyMap == null || policyMap.isEmpty()) {
            return;
        }
        for (Case c : cases) {
            if (c.Insurance_Policy__c == null && c.BusinessKey__c != null) {
                InsurancePolicy policy = policyMap.get(c.BusinessKey__c);
                if (policy != null) {
                    c.Insurance_Policy__c = policy.Id;
                    if('PU_FOLDER'.equalsIgnoreCase(policy.RecordType.DeveloperName) || 'PU_POSITION'.equalsIgnoreCase(policy.RecordType.DeveloperName)){
                        c.DrIdFolderPU__c = 'S';
                    }
                }
            }
        }
    }

	public static Map<String, Id> fetchGroupCip(Set<String> cipSet) {
        Map<String, Id> cipToGroupId = new Map<String, Id>();
        if (!cipSet.isEmpty() && Schema.sObjectType.Group__c.isAccessible()) {
            for (Group__c g : [SELECT Id, ExternalId__c FROM Group__c WHERE ExternalId__c IN :cipSet]) {
                cipToGroupId.put(g.ExternalId__c, g.Id);
            }
        } 
        return cipToGroupId;
    }

	public static Map<String, User> getUserByComponent(Set<String> groupComponentSet) {
		Map<String, User> userMap = new Map<String, User>();
		if (!groupComponentSet.isEmpty() && Schema.sObjectType.User.isAccessible()) {
			for (User u : [SELECT Id, ExternalId__c FROM User WHERE ExternalId__c IN :groupComponentSet]) {
				userMap.put(u.ExternalId__c, u);
			}
		}
		return userMap;
	}

    public static void checkPolicy(List<Case> newListCase) {
        for(Case c : newListCase){

            if(c.Insurance_Policy__c == null && c.BusinessKey__c != null && c.BusinessKey__c?.length() == 15 && c.DrPosizioniPU__c != null && Integer.valueOf(c.DrPosizioniPU__c) > 0 && c.DrIdFolderPU__c == null) {
                c.DrIdFolderPU__c = 'S';
            }
        }
    }
}