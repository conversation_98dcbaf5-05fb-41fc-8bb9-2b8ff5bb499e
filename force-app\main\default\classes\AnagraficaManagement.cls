global without sharing class AnagraficaManagement implements Callable{

    public  static final Map<String, String> stati = new Map<String, String>{'Z200' => '002',
																		    'Z100' => '087',
																		    'Z301' => '003',
																		    'Z725' => '148',
																		    'Z101' => '004',
																		    'Z302' => '133',
																		    'Z529' => '209',
																		    'Z532' => '197',
																		    'Z203' => '005',
																		    'Z600' => '006',
																		    'Z252' => '266',
																		    'Z501' => '212',
																		    'Z700' => '007',
																		    'Z102' => '008',
																		    'Z253' => '268',
																		    'Z502' => '160',
																		    'Z204' => '169',
																		    'Z249' => '130',
																		    'Z522' => '118',
																		    'Z103' => '009',
																		    'Z512' => '198',
																		    'Z314' => '158',
																		    'Z400' => '207',
																		    'Z205' => '097',
																		    'Z139' => '264',
																		    'Z601' => '010',
																		    'Z153' => '274',
																		    'Z358' => '098',
																		    'Z602' => '011',
																		    'Z104' => '012',
																		    'Z354' => '142',
																		    'Z305' => '025',
																		    'Z716' => '253',
																		    'Z208' => '135',
																		    'Z306' => '119',
																		    'Z401' => '013',
																		    'Z307' => '188',
																		    'Z309' => '144',
																		    'Z603' => '015',
																		    'Z210' => '016',
																		    'Z211' => '101',
																		    'Z106' => '093',
																		    'Z604' => '017',
																		    'Z310' => '176',
																		    'Z311' => '145',
																		    'Z312' => '018',
																		    'Z214' => '074',
																		    'Z213' => '084',
																		    'Z313' => '146',
																		    'Z503' => '019',
																		    'Z149' => '261',
																		    'Z504' => '020',
																		    'Z107' => '021',
																		    'Z900' => '000',
																		    'Z901' => '140',
																		    'Z800' => '000',
																		    'Z902' => '165',
																		    'Z903' => '000',
																		    'Z904' => '000',
																		    'Z801' => '000',
																		    'Z802' => '000',
																		    'Z905' => '172',
																		    'Z906' => '000',
																		    'Z361' => '113',
																		    'Z526' => '192',
																		    'Z605' => '024',
																		    'Z336' => '023',
																		    'Z506' => '064',
																		    'Z215' => '238',
																		    'Z215' => '796',
																		    'Z215' => '242',
																		    'Z215' => '241',
																		    'Z215' => '239',
																		    'Z215' => '240',
																		    'Z215' => '244',
																		    'Z215' => '243',
																		    'Z368' => '277',
																		    'Z144' => '257',
																		    'Z349' => '138',
																		    'Z315' => '026',
																		    'Z108' => '204',
																		    'Z704' => '161',
																		    'Z216' => '027',
																		    'Z109' => '028',
																		    'Z110' => '029',
																		    'Z316' => '157',
																		    'Z317' => '164',
																		    'Z254' => '267',
																		    'Z112' => '094',
																		    'Z260' => '000',
																		    'Z318' => '112',
																		    'Z507' => '082',
																		    'Z219' => '088',
																		    'Z113' => '102',
																		    'Z220' => '122',
																		    'Z115' => '032',
																		    'Z524' => '156',
																		    'Z402' => '200',
																		    'Z508' => '214',
																		    'Z706' => '154',
																		    'Z509' => '033',
																		    'Z319' => '137',
																		    'Z321' => '167',
																		    'Z320' => '185',
																		    'Z606' => '159',
																		    'Z607' => '000',
																		    'Z510' => '034',
																		    'Z511' => '035',
																		    'Z222' => '114',
																		    'Z223' => '129',
																		    'Z224' => '039',
																		    'Z225' => '038',
																		    'Z707' => '000',
																		    'Z116' => '040',
																		    'Z117' => '041',
																		    'Z702' => '282',
																		    'Z715' => '285',
																		    'Z530' => '211',
																		    'Z212' => '281',
																		    'Z703' => '237',
																		    'Z609' => '190',
																		    'Z711' => '217',
																		    'Z724' => '191',
																		    'Z519' => '210',
																		    'Z520' => '221',
																		    'Z525' => '249',
																		    'Z729' => '218',
																		    'Z226' => '182',
																		    'Z000' => '086',
																		    'Z124' => '202',
																		    'Z255' => '269',
																		    'Z322' => '116',
																		    'Z731' => '194',
																		    'Z160' => '291',
																		    'Z227' => '126',
																		    'Z256' => '270',
																		    'Z228' => '136',
																		    'Z359' => '089',
																		    'Z145' => '258',
																		    'Z229' => '095',
																		    'Z325' => '044',
																		    'Z326' => '045',
																		    'Z119' => '090',
																		    'Z146' => '259',
																		    'Z120' => '092',
																		    'Z231' => '059',
																		    'Z148' => '278',
																		    'Z327' => '104',
																		    'Z328' => '056',
																		    'Z247' => '106',
																		    'Z232' => '127',
																		    'Z329' => '149',
																		    'Z121' => '105',
																		    'Z122' => '203',
																		    'Z710' => '219',
																		    'Z330' => '107',
																		    'Z513' => '213',
																		    'Z331' => '141',
																		    'Z332' => '128',
																		    'Z360' => '226',
																		    'Z514' => '046',
																		    'Z735' => '215',
																		    'Z140' => '265',
																		    'Z123' => '091',
																		    'Z233' => '110',
																		    'Z159' => '290',
																		    'Z531' => '208',
																		    'Z333' => '134',
																		    'Z206' => '083',
																		    'Z300' => '206',
																		    'Z713' => '109',
																		    'Z234' => '115',
																		    'Z515' => '047',
																		    'Z334' => '150',
																		    'Z335' => '117',
																		    'Z714' => '205',
																		    'Z125' => '048',
																		    'Z719' => '049',
																		    'Z235' => '163',
																		    'Z126' => '050',
																		    'Z236' => '036',
																		    'Z734' => '216',
																		    'Z516' => '051',
																		    'Z730' => '186',
																		    'Z610' => '052',
																		    'Z611' => '053',
																		    'Z722' => '175',
																		    'Z723' => '225',
																		    'Z127' => '054',
																		    'Z128' => '055',
																		    'Z518' => '220',
																		    'Z237' => '168',
																		    'Z114' => '031',
																		    'Z156' => '275',
																		    'Z308' => '143',
																		    'Z907' => '297',
																		    'Z505' => '063',
																		    'Z324' => '247',
																		    'Z129' => '061',
																		    'Z338' => '151',
																		    'Z154' => '262',
																		    'Z339' => '166',
																		    'Z533' => '195',
																		    'Z527' => '199',
																		    'Z528' => '196',
																		    'Z726' => '131',
																		    'Z130' => '037',
																		    'Z340' => '254',
																		    'Z341' => '187',
																		    'Z343' => '152',
																		    'Z158' => '289',
																		    'Z342' => '189',
																		    'Z344' => '153',
																		    'Z248' => '147',
																		    'Z240' => '065',
																		    'Z155' => '276',
																		    'Z150' => '260',
																		    'Z345' => '066',
																		    'Z131' => '067',
																		    'Z209' => '085',
																		    'Z403' => '248',
																		    'Z404' => '069',
																		    'Z347' => '078',
																		    'Z348' => '070',
																		    'Z207' => '125',
																		    'Z608' => '124',
																		    'Z132' => '068',
																		    'Z133' => '071',
																		    'Z257' => '272',
																		    'Z217' => '022',
																		    'Z357' => '057',
																		    'Z161' => '279',
																		    'Z241' => '072',
																		    'Z242' => '287',
																		    'Z351' => '155',
																		    'Z727' => '236',
																		    'Z728' => '162',
																		    'Z612' => '120',
																		    'Z352' => '075',
																		    'Z243' => '076',
																		    'Z258' => '273',
																		    'Z732' => '193',
																		    'Z138' => '263',
																		    'Z353' => '132',
																		    'Z134' => '077',
																		    'Z613' => '080',
																		    'Z259' => '271',
																		    'Z733' => '121',
																		    'Z614' => '081',
																		    'Z251' => '062',
																		    'Z246' => '042',
																		    'Z355' => '058',
																		    'Z337' => '073'};

    public Object call(String action, Map<String, Object> args)
    {
        System.debug(action);
        System.debug(args);
        Map<String, Object> input = (Map<String, Object>)args.get('input');
        Map<String, Object> output = (Map<String, Object>)args.get('output');
        Map<String, Object> options = (Map<String, Object>)args.get('options');

        String tipoSoggetto = (String)input.get('tipoSoggetto');
        System.debug(tipoSoggetto);
        Boolean isProfessione = (Boolean)input.get('isProfessione');
        System.debug(isProfessione);    
        String respBody = (String)input.get('respBody');
        Object result = (tipoSoggetto == 'PF') ? generateAnagraficaPf(respBody, isProfessione) : generateAnagraficaPg(respBody, isProfessione);
        output.put('result', result);
        return result;
    } 

    public class respWrapper {
        public String idAcc;
        public String idAccAccRelAgency;
        public String idAccAccRelSociety;
        public String idAccAgencyDetails;
        public String idAccDetails;
        public String ciu;
        public String userId;
        public Boolean success; 
        public String errorMessage;

        public respWrapper(String idAcc, String idAccAccRelAgency, String idAccAccRelSociety, String idAccAgencyDetails, String idAccDetails, String ciu, String userId, Boolean success, String errorMessage) {
            this.idAcc = idAcc;
            this.idAccAccRelAgency = idAccAccRelAgency;
            this.idAccAccRelSociety = idAccAccRelSociety;
            this.idAccAgencyDetails = idAccAgencyDetails;
            this.idAccDetails = idAccDetails;
            this.ciu = ciu;
            this.userId = userId;
            this.success = success;
            this.errorMessage = errorMessage;
        }

        public respWrapper(String idAcc, Boolean success, String errorMessage) {
            this.idAcc = idAcc;
            this.success = success;
            this.errorMessage = errorMessage;
        }
    }

    public static String generateAnagraficaPf(String respBody, Boolean isProfessione) {

        //TODO: Refine the error handling, cheks for persona giuridica, and data insertion
        
        if(respBody == null) {
            return JSON.serialize(new respWrapper(null, false, 'Blank response'));
        }

        if(respBody.contains('error')) {
            return respBody;
        }

        System.debug('##RespBody##: ' + respBody);

        System.debug('generateAnagraficaPf');

        
        RootNoProf input;
        if (isProfessione) {
            input = (RootProfPF)JSON.deserialize(respBody, RootProfPF.class);
        } else {
            input = (RootNoProfPF)JSON.deserialize(respBody, RootNoProfPF.class);
        }

        Professione professione = input.getProfessione();
        List<AltriIndirizzi> altriIndirizzi = input.getAltriIndirizzi();
        System.debug('input: ' + input);

        String persAccountRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'Account' AND DeveloperName = 'PersonAccount' LIMIT 1].Id;
        System.debug('##persAccountRTId##: ' + persAccountRTId);    
        String indAccountDetailRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'AccountDetails__c' AND DeveloperName = 'Individual' LIMIT 1].Id;
        System.debug('##indAccountDetailRTId##: ' + indAccountDetailRTId);
        String agencyRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'Account' AND DeveloperName = 'Agency' LIMIT 1].Id;
        System.debug('##agencyRTId##: ' + agencyRTId);
        String companyRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'Account' AND DeveloperName = 'Society' LIMIT 1].Id;
        System.debug('##companyRTId##: ' + companyRTId);
        String indAccountAgencyDetailRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'AccountAgencyDetails__c' AND DeveloperName = 'Individual' LIMIT 1].Id;
        System.debug('##indAccountAgencyDetailRTId##: ' + indAccountAgencyDetailRTId);       
        String societyAarRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'FinServ__AccountAccountRelation__c' AND DeveloperName = 'AccountSociety' LIMIT 1].Id;
        System.debug('##societyAarRTId##: ' + societyAarRTId);
        String agencyAarRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'FinServ__AccountAccountRelation__c' AND DeveloperName = 'AccountAgency' LIMIT 1].Id;
        System.debug('##agencyAarRTId##: ' + agencyAarRTId);
        String agencySocietyAarRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'FinServ__AccountAccountRelation__c' AND DeveloperName = 'AgencySociety' LIMIT 1].Id;
        System.debug('##agencyAarRTId##: ' + agencyAarRTId);
        String indAccountDetailNPIRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'AccountDetailsNPI__c' AND DeveloperName = 'Individual' LIMIT 1].Id;
        System.debug('##indAccountDetailNPIRTId##: ' + indAccountDetailNPIRTId);

        Savepoint sp = Database.setSavepoint();

        String accId;

        Account acc = new Account();
        acc.RecordTypeId = persAccountRTId;

        System.debug('##input.anagrafica.cognome##: ' + input.anagrafica.cognome);
        System.debug('##input.anagrafica.nome##: ' + input.anagrafica.nome);

        acc.LastName = input.anagrafica.cognome;
        acc.FirstName = input.anagrafica.nome;
        acc.FinServ__TaxId__pc = input.anagrafica.codiceFiscale;
        acc.ExternalId__c = input.anagrafica.codiceFiscale;
        
        Schema.SObjectField accExtId = Account.ExternalId__c.getDescribe().getSObjectField();
        Database.UpsertResult sr = Database.upsert(acc, accExtId, false);
        if(!sr.isSuccess()){
            
            if(String.valueOf(sr.getErrors()[0].getStatusCode()) == 'DUPLICATE_VALUE'){
                System.debug('##Duplicate value##: ' + sr.getErrors()[0].getMessage());
                accId = [SELECT Id FROM Account WHERE ExternalId__c =: input.anagrafica.codiceFiscale LIMIT 1].Id;
            }else{
                return JSON.serialize(new respWrapper(null, false, 'Error account creation: ' + sr.getErrors()[0].getMessage()));
            }
        }else{
            accId = String.valueOf(sr.getId());
        }
        System.debug('###AccCreateUpsert: ' + sr.isCreated());
        System.debug('##accId##: ' + accId);

        System.debug('##input.datiAgenzia' + input.datiAgenzia);
        System.debug('##input.anagrafica' + input.anagrafica);
        

        DatiAgenzia datiAgenzia = input.datiAgenzia;
        Anagrafica anagrafica = input.anagrafica;

        System.debug('##anagrafica.datiTracciatura' + anagrafica.datiTracciatura);

        DatiTracciatura datiTracciatura = anagrafica.datiTracciatura;

        String companyCode = 'SOC_'+ datiTracciatura.compagniaCreazione;
        String companyCodeRaw = datiTracciatura.compagniaCreazione;
        String agencyCode = 'AGE_'+ datiAgenzia.codiceAgenzia;
        String agencyCodeRaw = datiAgenzia.codiceAgenzia;

        //Id agencyId = [SELECT Id FROM Account WHERE RecordTypeId = :agencyRTId AND (AgencyCode__c =: agencyCode OR AgencyCode__c =: agencyCodeRaw ) LIMIT 1].Id;
        Id companyId = [SELECT Id FROM Account WHERE RecordTypeId = :companyRTId AND (AgencyCode__c =: companyCode OR AgencyCode__c =: companyCodeRaw) LIMIT 1].Id;
        //Id agencyId = [SELECT FinServ__Account__c FROM FinServ__AccountAccountRelation__c WHERE RecordTypeId =: agencySocietyAarRTId AND Identifier__c =: agencyCodeRaw LIMIT 1].FinServ__Account__c;
        FinServ__AccountAccountRelation__c agency = [SELECT Id, FinServ__Account__c, FinServ__Account__r.ExternalId__c FROM FinServ__AccountAccountRelation__c WHERE RecordTypeId =: agencySocietyAarRTId AND Identifier__c =: agencyCodeRaw LIMIT 1];
        //Id companyId = [SELECT FinServ__RelatedAccount__c, FinServ__RelatedAccount__r.ExternalId__c FROM FinServ__AccountAccountRelation__c WHERE RecordTypeId =: agencySocietyAarRTId AND Identifier__c =: agencyCodeRaw LIMIT 1].FinServ__RelatedAccount__c;
        Account company = [SELECT id, ExternalId__c FROM Account WHERE id =: companyId];
        //String companyCode = company.ExternalId__c;       
        //String companyCodeRaw = (company.ExternalId__c).right(1);

        List<FinServ__ReciprocalRole__c> reciprocalRoles = [SELECT Id, Name, FinServ__InverseRole__c  FROM FinServ__ReciprocalRole__c ];

        List<FinServ__AccountAccountRelation__c> listAarToInsert = new List<FinServ__AccountAccountRelation__c>();

        FinServ__AccountAccountRelation__c aarCompany = new FinServ__AccountAccountRelation__c();
        aarCompany.FinServ__RelatedAccount__c = companyId;
        aarCompany.FinServ__Account__c = accId;
        aarCompany.FinServ__ExternalId__c = anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw;
        aarCompany.RecordTypeId = societyAarRTId;
 
        for(FinServ__ReciprocalRole__c rr : reciprocalRoles){
            if('Cliente'.equalsIgnoreCase(rr.Name) && 'Compagnia'.equalsIgnoreCase(rr.FinServ__InverseRole__c)){
                aarCompany.FinServ__Role__c = rr.Id;
            }
        }

        listAarToInsert.add(aarCompany);
 
        FinServ__AccountAccountRelation__c aarAgency = new FinServ__AccountAccountRelation__c();
        aarAgency.FinServ__RelatedAccount__c = agency.FinServ__Account__c;
        aarAgency.FinServ__Account__c = accId;
        aarAgency.FinServ__ExternalId__c = anagrafica.codiceFiscale + '_' + agency.FinServ__Account__r.ExternalId__c;
        aarAgency.RecordTypeId = agencyAarRTId;

        for(FinServ__ReciprocalRole__c rr : reciprocalRoles){
            if('Cliente'.equalsIgnoreCase(rr.Name) && 'Agenzia'.equalsIgnoreCase(rr.FinServ__InverseRole__c)){
                aarAgency.FinServ__Role__c = rr.Id;
            }
        }

        listAarToInsert.add(aarAgency);

        Schema.SObjectField aarExtId = FinServ__AccountAccountRelation__c.FinServ__ExternalId__c.getDescribe().getSObjectField();        
        Database.UpsertResult	[] saveResults = Database.upsert(listAarToInsert, aarExtId, false);

        List<String> aarErrors = new List<String>();

        for(Database.UpsertResult saveR : saveResults){
            if(!saveR.isSuccess()){
                for(Database.Error err : saveR.getErrors()){
                    System.debug('Error aar agency creation: ' + err.getMessage());
                    aarErrors.add(String.valueOf(err.getStatusCode()));
                }
            }
        }

        if(aarErrors.size() > 0){
            if(!aarErrors.contains('DUPLICATE_VALUE')){
                String err;
                for(String error : aarErrors){
                    err += error + ' ';
                }
                Database.rollback(sp);
                return JSON.serialize(new respWrapper(null, false, 'Error aar creation: ' + err));
            }
        }

        aarAgency = [SELECT Id, FinServ__RelatedAccount__c, FinServ__Account__c, FinServ__ExternalId__c, RecordTypeId FROM FinServ__AccountAccountRelation__c WHERE FinServ__ExternalId__c =: (anagrafica.codiceFiscale + '_' + agency.FinServ__Account__r.ExternalId__c) LIMIT 1];
        aarCompany = [SELECT Id, FinServ__RelatedAccount__c, FinServ__Account__c, FinServ__ExternalId__c, RecordTypeId FROM FinServ__AccountAccountRelation__c WHERE FinServ__ExternalId__c =: (anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw) LIMIT 1];

        System.debug('###AARCreateUpsert: ' + saveResults[0].isCreated() + ' ' + saveResults[1].isCreated());
        System.debug('##aarCompany' + aarCompany);
        System.debug('##aarAgency' + aarAgency);

        AccountAgencyDetails__c aad = new AccountAgencyDetails__c();
        aad.Relation__c = aarAgency.Id;
        aad.SourceSystemIdentifier__c = String.valueOf(anagrafica.ciu);
        aad.Name = anagrafica.codiceFiscale;
        if(companyCodeRaw == '4'){
            aad.ExternalId__c = 'PASS_' + anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw + '_' + agency.FinServ__Account__r.ExternalId__c;
        }else{
            aad.ExternalId__c = 'ANAG2_' + anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw + '_' + agency.FinServ__Account__r.ExternalId__c;
        }
        //aad.Mobile__c = input.contatti[0].contatto;
        aad.RecordTypeId = indAccountAgencyDetailRTId;
        aad.Society__c = companyId;

        //Dati anagrafici di canale
        aad.PrelevantAgencyCode__c = datiAgenzia.codiceAgenziaPrevalente;
        aad.EndDate__c = (datiAgenzia?.dataFineEffetto != null) ? Date.valueOf(datiAgenzia?.dataFineEffetto) : null; 
        aad.EffectiveDate__c = (datiAgenzia?.dataInizioEffetto != null) ? Date.valueof(datiAgenzia?.dataInizioEffetto) : null;
        aad.RegistryStatus__c = datiAgenzia?.statoSoggetto;
        aad.ClientTerminationDate__c = (datiAgenzia?.dataCessazioneCliente != null) ? Date.valueof(datiAgenzia?.dataCessazioneCliente) : null;
        aad.FlagBondAuthorization__c = (datiAgenzia?.flagAutorizzazioneCauzione != null) ? datiAgenzia?.flagAutorizzazioneCauzione : false;
        aad.SubAgencyCode__c = datiAgenzia?.codiceSubagenzia;
        aad.FeaFlagContactsOwnership__c = (datiAgenzia?.flagProprietaContattiFea != null) ? datiAgenzia?.flagProprietaContattiFea : false;
        aad.FeaFlagSubscription__c = (datiAgenzia?.flagAdesioneFEA != null) ? datiAgenzia?.flagAdesioneFEA : false;
        aad.TopClientDate__c = (datiAgenzia?.dataClienteTop != null) ? Date.valueOf(datiAgenzia?.dataClienteTop) : null;
        aad.TopClientFlag__c = (datiAgenzia?.flagClienteTop != null) ? datiAgenzia?.flagClienteTop : false;
        aad.SourceSystemCreatedDate__c = (datiAgenzia?.dataInizioEffetto != null) ? Date.valueof(datiAgenzia?.dataInizioEffetto) : null;


        //contatti
        for (Contatto c : input.contatti) {
            switch on c.tipoContatto {
                when 'CELL' {
                    aad.Mobile__c = c.contatto;
                    aad.MobileSourceSystemIdentifier__c = String.valueOf(c.id);
                    aad.MobileType__c = c.tipoContatto;
                    aad.MobileUsageType__c = c.tipologiaContatto;
                    aad.MobileFlagPreferred__c = c.flagPreferito;
                }
                when 'MAIL' {
                    aad.Email__c = c.contatto;
                    aad.EmailSourceSystemIdentifier__c = String.valueOf(c.id);
                    aad.EmailType__c = c.tipoContatto;
                    aad.EmailUsageType__c = c.tipologiaContatto;
                    aad.EmailFlagPreferred__c = c.flagPreferito;
                }
                when 'PEC' {
                    aad.CertifiedEmail__c = c.contatto;
                    aad.CertifiedEmailSourceSystemIdentifier__c = String.valueOf(c.id);
                    aad.CertifiedEmailType__c = c.tipoContatto;
                    aad.CertifiedEmailUsageType__c = c.tipologiaContatto;
                    aad.CertifiedEmailFlagPreferred__c = c.flagPreferito;
                }
                when else {
                    continue;
                }
            }
        }

        Schema.SObjectField aadExtId = AccountAgencyDetails__c.ExternalId__c.getDescribe().getSObjectField();
        Database.UpsertResult srAad = Database.upsert(aad, aadExtId, false);
        if(!srAad.isSuccess()){
            for(Database.Error err : srAad.getErrors()){
                System.debug('Error aad creation: ' + err.getMessage());
            }

            if(String.valueOf(srAad.getErrors()[0].getStatusCode()) != 'DUPLICATE_VALUE'){
                Database.rollback(sp);
                return JSON.serialize(new respWrapper(null, false, 'Error aad creation: ' + srAad.getErrors()[0].getMessage()));         
            }
        }
        aad = [SELECT Id, Relation__c, SourceSystemIdentifier__c, Name, ExternalId__c, Mobile__c, Email__c, CertifiedEmail__c FROM AccountAgencyDetails__c WHERE ExternalId__c =: ('ANAG2_' + anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw + '_' + agency.FinServ__Account__r.ExternalId__c) LIMIT 1];

        System.debug('###AADCreateUpsert: ' + srAad.isCreated());
        System.debug('##aad' + aad);

        //AccountDetailsNPI

        AccountDetailsNPI__c adNPI = new AccountDetailsNPI__c();
        adNPI.RecordTypeId = indAccountDetailNPIRTId;
        adNPI.Name = anagrafica.Nome + ' ' + anagrafica.Cognome;
        adNPI.Relation__c = aarCompany.Id;
        adNPI.FiscalCode__c = anagrafica.codiceFiscale;
        if(companyCodeRaw == '4'){
            adNPI.ExternalId__c = 'PASS_' + anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw;
        }else{
            adNPI.ExternalId__c = 'ANAG2_' + anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw;
        }
        //Altri Indirizzi
        adNPI.OtherCensusDistrict__c = altriIndirizzi[0]?.distrettoCensuario;
        adNPI.OtherLatitude__c = altriIndirizzi[0]?.latitudine;
        adNPI.OtherLongitude__c = altriIndirizzi[0]?.longitudine;
        adNPI.OtherAt__c = altriIndirizzi[0]?.presso;
        adNPI.OtherStreet__c = altriIndirizzi[0]?.indirizzoCompleto;
        adNPI.OtherAddressNormalizationType__c = altriIndirizzi[0]?.tipoNormalizzato;
        adNPI.OtherDus__c = altriIndirizzi[0]?.dus;
        adNPI.OtherStreetNumber__c = altriIndirizzi[0]?.numeroCivico;
        adNPI.OtherDug__c = altriIndirizzi[0]?.dug;
        adNPI.OtherCity__c = altriIndirizzi[0]?.localita;
        adNPI.OtherShortCity__c = altriIndirizzi[0]?.localitaBreve;
        adNPI.OtherCadastralCode__c = altriIndirizzi[0]?.codiceBelfioreComune;
        adNPI.OtherPostalCode__c = altriIndirizzi[0]?.codicePostale;
        adNPI.OtherIstatCode__c = altriIndirizzi[0]?.codiceIstatComune;
        adNPI.OtherState__c = altriIndirizzi[0]?.abbreviazioneProvincia;
        adNPI.OtherIstatCodeAnag1__c = altriIndirizzi[0]?.codiceIstatAnag1;
        adNPI.OtherAddressType__c = altriIndirizzi[0]?.tipoIndirizzo;
        adNPI.OtherCadestralCountryCode__c = altriIndirizzi[0]?.codiceBelfioreStato;
        adNPI.OtherAddressSourceSystemIdentifier__c = (altriIndirizzi[0]?.id != null) ? String.valueOf(altriIndirizzi[0]?.id) : null;

        Schema.SObjectField adNPIExtId = AccountDetailsNPI__c.ExternalId__c.getDescribe().getSObjectField();
        Database.UpsertResult srAdNPI = Database.upsert(adNPI, adNPIExtId, false);
        if(!srAdNPI.isSuccess()){
            for(Database.Error err : srAdNPI.getErrors()){
                System.debug('Error ad creation: ' + err.getMessage());
            }
            if(String.valueOf(srAdNPI.getErrors()[0].getStatusCode()) != 'DUPLICATE_VALUE'){
                Database.rollback(sp);
                return JSON.serialize(new respWrapper(null, false, 'Error ad creation: ' + srAdNPI.getErrors()[0].getMessage()));
            }
        }

        adNPI = [SELECT Id, Relation__c, Name, FiscalCode__c, ExternalId__c, OtherCensusDistrict__c, OtherLatitude__c, OtherLongitude__c, OtherAt__c, OtherStreet__c, OtherAddressNormalizationType__c, OtherDus__c, OtherStreetNumber__c, OtherDug__c, OtherCity__c, OtherShortCity__c, OtherCadastralCode__c, OtherPostalCode__c, OtherIstatCode__c, OtherState__c, OtherIstatCodeAnag1__c, OtherAddressType__c, OtherCadestralCountryCode__c FROM AccountDetailsNPI__c WHERE ExternalId__c =: ('ANAG2_' + anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw) LIMIT 1];

        System.debug('###NPICreateUpsert: ' + srAdNPI.isCreated());
        System.debug('##adNPI' + adNPI);


        String gender;
        if('Maschio'.equalsIgnoreCase(anagrafica.sesso) || 'M'.equalsIgnoreCase(anagrafica.sesso)){
            gender = 'Male';
        }else if ('Femmina'.equalsIgnoreCase(anagrafica.sesso) || 'F'.equalsIgnoreCase(anagrafica.sesso)){
            gender = 'Female';
        }

        AccountDetails__c ad = new AccountDetails__c();
        ad.RecordTypeId = indAccountDetailRTId;
        ad.Relation__c = aarCompany.Id;
        ad.AccountDetailsNPI__c = adNPI.Id;
        ad.SourceSystemIdentifier__c = String.valueOf(anagrafica.ciu);
        if(companyCodeRaw == '4'){
            ad.ExternalId__c = 'PASS_' + anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw;
        }else{
            ad.ExternalId__c = 'ANAG2_' + anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw;
        }
        ad.Name = anagrafica.Nome + ' ' + anagrafica.Cognome;
        ad.FiscalCode__c = anagrafica.codiceFiscale;
        ad.FullName__c = anagrafica.nome + ' ' + anagrafica.cognome;
        ad.FirstName__c = anagrafica.nome;
        ad.LastName__c = anagrafica.cognome;
        ad.Gender__c = gender;
        ad.BirthProvince__c = anagrafica.provinciaNascita;
        ad.BirthPlace__c = anagrafica.belfioreComuneNascita;
        ad.ProfessionCode__c = (isProfessione) ? professione?.professione?.codice : null; //ho modificato qui
        ad.ProfessionSectorType__c = professione?.settore?.codice;
        ad.ProfessionEmploymentType__c = professione?.impiego?.codice;
        ad.ProfessionSpecializationType__c = professione?.specializzazione?.codice;
        ad.BirthDate__c = Date.valueOf(anagrafica.dataNascita);
        ad.BirthCountry__c = (anagrafica?.nazioneNascita != null) ? stati.get(anagrafica?.nazioneNascita) : null;
        ad.SourceSystemRegistryType__c = anagrafica?.tipoAnagrafica;
        ad.SourceSystemStatus__c = anagrafica?.statoAnagrafica;
        ad.VatNumberStatus__c = anagrafica?.statoPartitaIVA;
        ad.FiscalCodeStatus__c = anagrafica?.statoCodiceFiscale;
        ad.SourceSystemType__c = anagrafica?.tipoSoggetto;
        //Indirizzo
        ad.CensusDistrict__c = anagrafica?.indirizzo?.distrettoCensuario;
        ad.Latitude__c = anagrafica?.indirizzo?.latitudine;
        ad.Longitude__c = anagrafica?.indirizzo?.longitudine;
        ad.At__c = anagrafica?.indirizzo?.presso;
        ad.Street__c = anagrafica?.indirizzo?.indirizzoCompleto;
        ad.AddressNormalizationType__c = anagrafica?.indirizzo?.tipoNormalizzato;
        ad.Dus__c = anagrafica?.indirizzo?.dus;
        ad.StreetNumber__c = anagrafica?.indirizzo?.numeroCivico;
        ad.Dug__c = anagrafica?.indirizzo?.dug;
        ad.City__c = anagrafica?.indirizzo?.localita;
        ad.ShortCity__c = anagrafica?.indirizzo?.localitaBreve;
        ad.CadastralCode__c = anagrafica?.indirizzo?.codiceBelfioreComune;
        ad.PostalCode__c = anagrafica?.indirizzo?.codicePostale;
        ad.IstatCode__c = anagrafica?.indirizzo?.codiceIstatComune;
        ad.State__c = anagrafica?.indirizzo?.abbreviazioneProvincia;
        ad.IstatCodeAnag1__c = anagrafica?.indirizzo?.codiceIstatAnag1;
        ad.AddressType__c = anagrafica?.indirizzo?.tipoIndirizzo;
        ad.CadestralCountryCode__c = anagrafica?.indirizzo?.codiceBelfioreStato;
        ad.AddressSourceSystemIdentifier__c = (anagrafica?.indirizzo?.id != null) ? String.valueOf(anagrafica?.indirizzo?.id) : null;
        //Altri Indirizzi
        ad.OtherCensusDistrict__c = altriIndirizzi[0]?.distrettoCensuario;
        ad.OtherLatitude__c = altriIndirizzi[0]?.latitudine;
        ad.OtherLongitude__c = altriIndirizzi[0]?.longitudine;
        ad.OtherAt__c = altriIndirizzi[0]?.presso;
        ad.OtherStreet__c = altriIndirizzi[0]?.indirizzoCompleto;
        ad.OtherAddressNormalizationType__c = altriIndirizzi[0]?.tipoNormalizzato;
        ad.OtherDus__c = altriIndirizzi[0]?.dus;
        ad.OtherStreetNumber__c = altriIndirizzi[0]?.numeroCivico;
        ad.OtherDug__c = altriIndirizzi[0]?.dug;
        ad.OtherCity__c = altriIndirizzi[0]?.localita;
        ad.OtherShortCity__c = altriIndirizzi[0]?.localitaBreve;
        ad.OtherCadastralCode__c = altriIndirizzi[0]?.codiceBelfioreComune;
        ad.OtherPostalCode__c = altriIndirizzi[0]?.codicePostale;
        ad.OtherIstatCode__c = altriIndirizzi[0]?.codiceIstatComune;
        ad.OtherState__c = altriIndirizzi[0]?.abbreviazioneProvincia;
        ad.OtherIstatCodeAnag1__c = altriIndirizzi[0]?.codiceIstatAnag1;
        ad.OtherAddressType__c = altriIndirizzi[0]?.tipoIndirizzo;
        ad.OtherCadestralCountryCode__c = altriIndirizzi[0]?.codiceBelfioreStato;
        ad.OtherAddressSourceSystemIdentifier__c = (altriIndirizzi[0]?.id != null) ? String.valueOf(altriIndirizzi[0]?.id) : null;
        //Privacy
        ad.SourceSystemConsentCode__c = input?.privacy?.datiAdesione?.tipoPrivacy;
        //ad.SourceSystemConsentEffectiveDate__c = input?.privacy?.datiAdesione?.SourceSystemConsentEffectiveDate__c;
        //ad.SourceSystemConsentEndDate__c = input?.privacy?.datiAdesione?.SourceSystemConsentEndDate__c;
        //Optout
        ad.OptOutStartApplication__c = input?.privacy?.datiOptOut?.applicazioneFine;
        ad.OptOutEndApplication__c = input?.privacy?.datiOptOut?.applicazioneInizio;
        ad.OptOutType__c = input?.privacy?.datiOptOut?.tipoOptOut;
        ad.OptOutEffectiveDate__c = (input?.privacy?.datiOptOut?.dataInizioEffetto != null) ? Date.valueOf(input?.privacy?.datiOptOut?.dataInizioEffetto) : null;
        ad.OptOutEndDate__c = (input?.privacy?.datiOptOut?.dataFineEffetto != null) ? Date.valueOf(input?.privacy?.datiOptOut?.dataFineEffetto) : null;

        Schema.SObjectField adExtId = AccountDetails__c.ExternalId__c.getDescribe().getSObjectField();
        Database.UpsertResult srAd = Database.upsert(ad, adExtId, false);
        if(!srAd.isSuccess()){
            for(Database.Error err : srAd.getErrors()){
                System.debug('Error ad creation: ' + err.getMessage());
            }
            if(String.valueOf(srAd.getErrors()[0].getStatusCode()) != 'DUPLICATE_VALUE'){
                Database.rollback(sp);
                return JSON.serialize(new respWrapper(null, false, 'Error ad creation: ' + srAd.getErrors()[0].getMessage()));
            }
        }

        ad = [SELECT Id, Relation__c, SourceSystemIdentifier__c, Name, ExternalId__c, VatNumber__c, FullName__c, SourceSystemType__c, ProfessionCode__c, ProfessionLegalEntityType__c, CompanyType__c, SourceSystemRegistryType__c, SourceSystemStatus__c, VatNumberStatus__c, FiscalCodeStatus__c, CensusDistrict__c, Latitude__c, Longitude__c, At__c, Street__c, AddressNormalizationType__c, Dus__c, StreetNumber__c, Dug__c, City__c, ShortCity__c, CadastralCode__c, PostalCode__c, IstatCode__c, State__c, IstatCodeAnag1__c, AddressType__c, CadestralCountryCode__c FROM AccountDetails__c WHERE ExternalId__c =: ('ANAG2_' + anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw) LIMIT 1];

        System.debug('###AdCreateUpsert: ' + srAd.isCreated());
        System.debug('##ad' + ad);

        String resp = JSON.serialize(new respWrapper(accId, aarAgency.Id, aarCompany.Id, aad.Id, ad.Id, ad.SourceSystemIdentifier__c, String.valueOf(Userinfo.getUserId()), true, null));
        System.debug('##resp##: ' + resp);

        return resp;
    }

    public static String generateAnagraficaPg(String respBody, Boolean isProfessione) {

        //TODO: Refine the error handling, cheks for persona giuridica, and data insertion
        
        if(respBody == null) {
            return JSON.serialize(new respWrapper(null, false, 'Blank response'));
        }

        if(respBody.contains('error')) {
            return respBody;
        }

        System.debug('##RespBody##: ' + respBody);

        System.debug('generateAnagraficaPg');

        RootNoProf input;
        if (isProfessione) {
            input = (RootProf)JSON.deserialize(respBody, RootProf.class);
        } else {
            input = (RootNoProf)JSON.deserialize(respBody, RootNoProf.class);
        }

        Professione professione = input.getProfessione();

        System.debug('input: ' + input);

        String BusinessRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'Account' AND DeveloperName = 'IndustriesBusiness' LIMIT 1].Id;
        System.debug('##BusinessRTId##: ' + BusinessRTId);    
        String BusAccountDetailRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'AccountDetails__c' AND DeveloperName = 'Business' LIMIT 1].Id;
        System.debug('##BusAccountDetailRTId##: ' + BusAccountDetailRTId);
        String agencyRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'Account' AND DeveloperName = 'Agency' LIMIT 1].Id;
        System.debug('##agencyRTId##: ' + agencyRTId);
        String companyRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'Account' AND DeveloperName = 'Society' LIMIT 1].Id;
        System.debug('##companyRTId##: ' + companyRTId);
        String busAccountAgencyDetailRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'AccountAgencyDetails__c' AND DeveloperName = 'Business' LIMIT 1].Id;
        System.debug('##busAccountAgencyDetailRTId##: ' + busAccountAgencyDetailRTId);       
        String societyAarRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'FinServ__AccountAccountRelation__c' AND DeveloperName = 'AccountSociety' LIMIT 1].Id;
        System.debug('##societyAarRTId##: ' + societyAarRTId);
        String agencyAarRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'FinServ__AccountAccountRelation__c' AND DeveloperName = 'AccountAgency' LIMIT 1].Id;
        System.debug('##agencyAarRTId##: ' + agencyAarRTId);
        String agencySocietyAarRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'FinServ__AccountAccountRelation__c' AND DeveloperName = 'AgencySociety' LIMIT 1].Id;
        System.debug('##agencyAarRTId##: ' + agencyAarRTId);
        String BusAccountDetailNPIRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'AccountDetailsNPI__c' AND DeveloperName = 'Business' LIMIT 1].Id;
        System.debug('##BusAccountDetailNPIRTId##: ' + BusAccountDetailNPIRTId);

        Savepoint sp = Database.setSavepoint();

        String accId;

        Account acc = new Account();
        acc.RecordTypeId = BusinessRTId;

        System.debug('##input.anagrafica.cognome##: ' + input.anagrafica.cognome);
        System.debug('##input.anagrafica.nome##: ' + input.anagrafica.nome);

        acc.Name = input.anagrafica.ragioneSociale;
        acc.FullName__c = input.anagrafica.ragioneSociale;
        acc.VatNumber__c = input.anagrafica.codiceFiscale;
        acc.ExternalId__c = input.anagrafica.codiceFiscale;
        
        Schema.SObjectField accExtId = Account.ExternalId__c.getDescribe().getSObjectField();
        Database.UpsertResult sr = Database.upsert(acc, accExtId, false);
        if(!sr.isSuccess()){
            
            if(String.valueOf(sr.getErrors()[0].getStatusCode()) == 'DUPLICATE_VALUE'){
                System.debug('##Duplicate value##: ' + sr.getErrors()[0].getMessage());
                accId = [SELECT Id FROM Account WHERE ExternalId__c =: input.anagrafica.codiceFiscale LIMIT 1].Id;
            }else{
                return JSON.serialize(new respWrapper(null, false, 'Error account creation: ' + sr.getErrors()[0].getMessage()));
            }
        }else{
            accId = String.valueOf(sr.getId());
        }
        System.debug('###AccCreateUpsert: ' + sr.isCreated());
        System.debug('##accId##: ' + accId);

        System.debug('##input.datiAgenzia' + input.datiAgenzia);
        System.debug('##input.anagrafica' + input.anagrafica);
        

        DatiAgenzia datiAgenzia = input.datiAgenzia;
        Anagrafica anagrafica = input.anagrafica;

        System.debug('##anagrafica.datiTracciatura' + anagrafica.datiTracciatura);

        DatiTracciatura datiTracciatura = anagrafica.datiTracciatura;

        String companyCode = 'SOC_'+ datiTracciatura.compagniaCreazione;
        String companyCodeRaw = datiTracciatura.compagniaCreazione;
        String agencyCode = 'AGE_'+ datiAgenzia.codiceAgenzia;
        String agencyCodeRaw = datiAgenzia.codiceAgenzia;

        //Id agencyId = [SELECT Id FROM Account WHERE RecordTypeId = :agencyRTId AND (AgencyCode__c =: agencyCode OR AgencyCode__c =: agencyCodeRaw ) LIMIT 1].Id;
        Id companyId = [SELECT Id FROM Account WHERE RecordTypeId = :companyRTId AND (AgencyCode__c =: companyCode OR AgencyCode__c =: companyCodeRaw) LIMIT 1].Id;
        //Id agencyId = [SELECT FinServ__Account__c FROM FinServ__AccountAccountRelation__c WHERE RecordTypeId =: agencySocietyAarRTId AND Identifier__c =: agencyCodeRaw LIMIT 1].FinServ__Account__c;
        FinServ__AccountAccountRelation__c agency = [SELECT Id, FinServ__Account__c, FinServ__Account__r.ExternalId__c FROM FinServ__AccountAccountRelation__c WHERE RecordTypeId =: agencySocietyAarRTId AND Identifier__c =: agencyCodeRaw LIMIT 1];
        //Id companyId = [SELECT FinServ__RelatedAccount__c, FinServ__RelatedAccount__r.ExternalId__c FROM FinServ__AccountAccountRelation__c WHERE RecordTypeId =: agencySocietyAarRTId AND Identifier__c =: agencyCodeRaw LIMIT 1].FinServ__RelatedAccount__c;
        Account company = [SELECT id, ExternalId__c FROM Account WHERE id =: companyId];
        //String companyCode = company.ExternalId__c;       
        //String companyCodeRaw = (company.ExternalId__c).right(1);

        List<FinServ__ReciprocalRole__c> reciprocalRoles = [SELECT Id, Name, FinServ__InverseRole__c  FROM FinServ__ReciprocalRole__c ];

        List<FinServ__AccountAccountRelation__c> listAarToInsert = new List<FinServ__AccountAccountRelation__c>();

        FinServ__AccountAccountRelation__c aarCompany = new FinServ__AccountAccountRelation__c();
        aarCompany.FinServ__RelatedAccount__c = companyId;
        aarCompany.FinServ__Account__c = accId;
        aarCompany.FinServ__ExternalId__c = anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw;
        aarCompany.RecordTypeId = societyAarRTId;
 
        for(FinServ__ReciprocalRole__c rr : reciprocalRoles){
            if('Cliente'.equalsIgnoreCase(rr.Name) && 'Compagnia'.equalsIgnoreCase(rr.FinServ__InverseRole__c)){
                aarCompany.FinServ__Role__c = rr.Id;
            }
        }

        listAarToInsert.add(aarCompany);
 
        FinServ__AccountAccountRelation__c aarAgency = new FinServ__AccountAccountRelation__c();
        aarAgency.FinServ__RelatedAccount__c = agency.FinServ__Account__c;
        aarAgency.FinServ__Account__c = accId;
        aarAgency.FinServ__ExternalId__c = anagrafica.codiceFiscale + '_' + agency.FinServ__Account__r.ExternalId__c;
        aarAgency.RecordTypeId = agencyAarRTId;

        for(FinServ__ReciprocalRole__c rr : reciprocalRoles){
            if('Cliente'.equalsIgnoreCase(rr.Name) && 'Agenzia'.equalsIgnoreCase(rr.FinServ__InverseRole__c)){
                aarAgency.FinServ__Role__c = rr.Id;
            }
        }

        listAarToInsert.add(aarAgency);

        Schema.SObjectField aarExtId = FinServ__AccountAccountRelation__c.FinServ__ExternalId__c.getDescribe().getSObjectField();        
        Database.UpsertResult	[] saveResults = Database.upsert(listAarToInsert, aarExtId, false);

        List<String> aarErrors = new List<String>();

        for(Database.UpsertResult saveR : saveResults){
            if(!saveR.isSuccess()){
                for(Database.Error err : saveR.getErrors()){
                    System.debug('Error aar agency creation: ' + err.getMessage());
                    aarErrors.add(String.valueOf(err.getStatusCode()));
                }
            }
        }

        if(aarErrors.size() > 0){
            if(!aarErrors.contains('DUPLICATE_VALUE')){
                String err;
                for(String error : aarErrors){
                    err += error + ' ';
                }
                Database.rollback(sp);
                return JSON.serialize(new respWrapper(null, false, 'Error aar creation: ' + err));
            }
        }

        aarAgency = [SELECT Id, FinServ__RelatedAccount__c, FinServ__Account__c, FinServ__ExternalId__c, RecordTypeId FROM FinServ__AccountAccountRelation__c WHERE FinServ__ExternalId__c =: (anagrafica.codiceFiscale + '_' + agency.FinServ__Account__r.ExternalId__c) LIMIT 1];
        aarCompany = [SELECT Id, FinServ__RelatedAccount__c, FinServ__Account__c, FinServ__ExternalId__c, RecordTypeId FROM FinServ__AccountAccountRelation__c WHERE FinServ__ExternalId__c =: (anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw) LIMIT 1];

        System.debug('###AARCreateUpsert: ' + saveResults[0].isCreated() + ' ' + saveResults[1].isCreated());
        System.debug('##aarCompany' + aarCompany);
        System.debug('##aarAgency' + aarAgency);

        AccountAgencyDetails__c aad = new AccountAgencyDetails__c();
        aad.Relation__c = aarAgency.Id;
        aad.SourceSystemIdentifier__c = String.valueOf(anagrafica.ciu);
        aad.Name = anagrafica.codiceFiscale;
        if(companyCodeRaw == '4'){
            aad.ExternalId__c = 'PASS_' + anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw + '_' + agency.FinServ__Account__r.ExternalId__c;
        }else{
            aad.ExternalId__c = 'ANAG2_' + anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw + '_' + agency.FinServ__Account__r.ExternalId__c;
        }
        //aad.Mobile__c = input.contatti[0].contatto;
        aad.RecordTypeId = busAccountAgencyDetailRTId;
        aad.Society__c = companyId;

        //Dati anagrafici di canale
        aad.PrelevantAgencyCode__c = datiAgenzia.codiceAgenziaPrevalente;
        aad.EndDate__c = (datiAgenzia?.dataFineEffetto != null) ? Date.valueOf(datiAgenzia?.dataFineEffetto) : null; 
        aad.EffectiveDate__c = (datiAgenzia?.dataInizioEffetto != null) ? Date.valueof(datiAgenzia?.dataInizioEffetto) : null;
        aad.RegistryStatus__c = datiAgenzia?.statoSoggetto;
        aad.ClientTerminationDate__c = (datiAgenzia?.dataCessazioneCliente != null) ? Date.valueof(datiAgenzia?.dataCessazioneCliente) : null;
        aad.FlagBondAuthorization__c = (datiAgenzia?.flagAutorizzazioneCauzione != null) ? datiAgenzia?.flagAutorizzazioneCauzione : false;
        aad.SubAgencyCode__c = datiAgenzia?.codiceSubagenzia;
        aad.FeaFlagContactsOwnership__c = (datiAgenzia?.flagProprietaContattiFea != null) ? datiAgenzia?.flagProprietaContattiFea : false;
        aad.FeaFlagSubscription__c = (datiAgenzia?.flagAdesioneFEA != null) ? datiAgenzia?.flagAdesioneFEA : false;
        aad.TopClientDate__c = (datiAgenzia?.dataClienteTop != null) ? Date.valueOf(datiAgenzia?.dataClienteTop) : null;
        aad.TopClientFlag__c = (datiAgenzia?.flagClienteTop != null) ? datiAgenzia?.flagClienteTop : false;
        aad.SourceSystemCreatedDate__c = (datiAgenzia?.dataInizioEffetto != null) ? Date.valueof(datiAgenzia?.dataInizioEffetto) : null;

        //contatti
        for (Contatto c : input.contatti) {
            switch on c.tipoContatto {
                when 'CELL' {
                    aad.Mobile__c = c.contatto;
                    aad.MobileSourceSystemIdentifier__c = String.valueOf(c.id);
                    aad.MobileType__c = c.tipoContatto;
                    aad.MobileUsageType__c = c.tipologiaContatto;
                    aad.MobileFlagPreferred__c = c.flagPreferito;
                }
                when 'MAIL' {
                    aad.Email__c = c.contatto;
                    aad.EmailSourceSystemIdentifier__c = String.valueOf(c.id);
                    aad.EmailType__c = c.tipoContatto;
                    aad.EmailUsageType__c = c.tipologiaContatto;
                    aad.EmailFlagPreferred__c = c.flagPreferito;
                }
                when 'PEC' {
                    aad.CertifiedEmail__c = c.contatto;
                    aad.CertifiedEmailSourceSystemIdentifier__c = String.valueOf(c.id);
                    aad.CertifiedEmailType__c = c.tipoContatto;
                    aad.CertifiedEmailUsageType__c = c.tipologiaContatto;
                    aad.CertifiedEmailFlagPreferred__c = c.flagPreferito;
                }
                when else {
                    continue;
                }
            }
        }
        Schema.SObjectField aadExtId = AccountAgencyDetails__c.ExternalId__c.getDescribe().getSObjectField();
        Database.UpsertResult srAad = Database.upsert(aad, aadExtId, false);

        if(!srAad.isSuccess()){
            for(Database.Error err : srAad.getErrors()){
                System.debug('Error aad creation: ' + err.getMessage());
            }

            if(String.valueOf(srAad.getErrors()[0].getStatusCode()) != 'DUPLICATE_VALUE'){
                Database.rollback(sp);
                return JSON.serialize(new respWrapper(null, false, 'Error aad creation: ' + srAad.getErrors()[0].getMessage()));         
            }
        }
        aad = [SELECT Id, Relation__c, SourceSystemIdentifier__c, Name, ExternalId__c, Mobile__c, Email__c, CertifiedEmail__c FROM AccountAgencyDetails__c WHERE ExternalId__c =: ('ANAG2_' + anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw + '_' + agency.FinServ__Account__r.ExternalId__c) LIMIT 1];

        System.debug('###AADCreateUpsert: ' + srAad.isCreated());
        System.debug('##aad' + aad);


        //AccountDetailsNPI

        AccountDetailsNPI__c adNPI = new AccountDetailsNPI__c();
        adNPI.RecordTypeId = BusAccountDetailNPIRTId;
        adNPI.Name = anagrafica.Nome + ' ' + anagrafica.Cognome;
        adNPI.Relation__c = aarCompany.Id;
        adNPI.FiscalCode__c = anagrafica.codiceFiscale;
        if(companyCodeRaw == '4'){
            adNPI.ExternalId__c = 'PASS_' + anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw;
        }else{
            adNPI.ExternalId__c = 'ANAG2_' + anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw;
        } 

        Schema.SObjectField adNPIExtId = AccountDetailsNPI__c.ExternalId__c.getDescribe().getSObjectField();
        Database.UpsertResult srAdNPI = Database.upsert(adNPI, adNPIExtId, false);
        if(!srAdNPI.isSuccess()){
            for(Database.Error err : srAdNPI.getErrors()){
                System.debug('Error ad creation: ' + err.getMessage());
            }
            if(String.valueOf(srAdNPI.getErrors()[0].getStatusCode()) != 'DUPLICATE_VALUE'){
                Database.rollback(sp);
                return JSON.serialize(new respWrapper(null, false, 'Error ad creation: ' + srAdNPI.getErrors()[0].getMessage()));
            }
        }
        adNPI = [SELECT Id, Relation__c, Name, FiscalCode__c, ExternalId__c, OtherCensusDistrict__c, OtherLatitude__c, OtherLongitude__c, OtherAt__c, OtherStreet__c, OtherAddressNormalizationType__c, OtherDus__c, OtherStreetNumber__c, OtherDug__c, OtherCity__c, OtherShortCity__c, OtherCadastralCode__c, OtherPostalCode__c, OtherIstatCode__c, OtherState__c, OtherIstatCodeAnag1__c, OtherAddressType__c, OtherCadestralCountryCode__c FROM AccountDetailsNPI__c WHERE ExternalId__c =: ('ANAG2_' + anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw) LIMIT 1];

        System.debug('###NPICreateUpsert: ' + srAdNPI.isCreated());
        System.debug('##adNPI' + adNPI);

        String gender;
        if('Maschio'.equalsIgnoreCase(anagrafica.sesso)){
            gender = 'Female';
        }else if ('Femmina'.equalsIgnoreCase(anagrafica.sesso)){
            gender = 'Male';
        }

        AccountDetails__c ad = new AccountDetails__c();
        ad.RecordTypeId = BusAccountDetailRTId;
        ad.Relation__c = aarCompany.Id;
        ad.AccountDetailsNPI__c = adNPI.Id;
        ad.SourceSystemIdentifier__c = String.valueOf(anagrafica.ciu);
        if(companyCodeRaw == '4'){
            ad.ExternalId__c = 'PASS_' + anagrafica.partitaIVA + '_SOC_' + companyCodeRaw;
        }else{
            ad.ExternalId__c = 'ANAG2_' + anagrafica.partitaIVA + '_SOC_' + companyCodeRaw;
        }
        ad.Name = anagrafica.ragioneSociale;
        ad.VatNumber__c = anagrafica.partitaIVA;
        ad.FullName__c = anagrafica.ragioneSociale;
        ad.SourceSystemType__c = anagrafica?.tipoSoggetto;
        //ad.FirstName__c = anagrafica.nome;
        //ad.LastName__c = anagrafica.cognome;
        //ad.Gender__c = gender;
        //ad.BirthProvince__c = anagrafica.provinciaNascita;
        //ad.BirthPlace__c = anagrafica.comuneNascita;
        ad.ProfessionCode__c = (isProfessione) ? professione?.professione?.codice : null;
        ad.ProfessionLegalEntityType__c = professione?.personaGiuridica?.codice;
        ad.CompanyType__c = anagrafica?.tipoFormaSocietaria;
        ad.SourceSystemRegistryType__c = anagrafica?.tipoAnagrafica;
        ad.SourceSystemStatus__c = anagrafica?.statoAnagrafica;
        ad.VatNumberStatus__c = anagrafica?.statoPartitaIVA;
        ad.FiscalCodeStatus__c = anagrafica?.statoCodiceFiscale;
        //Indirizzi
        ad.CensusDistrict__c = anagrafica?.indirizzo?.distrettoCensuario;
        ad.Latitude__c = anagrafica?.indirizzo?.latitudine;
        ad.Longitude__c = anagrafica?.indirizzo?.longitudine;
        ad.At__c = anagrafica?.indirizzo?.presso;
        ad.Street__c = anagrafica?.indirizzo?.indirizzoCompleto;
        ad.AddressNormalizationType__c = anagrafica?.indirizzo?.tipoNormalizzato;
        ad.Dus__c = anagrafica?.indirizzo?.dus;
        ad.StreetNumber__c = anagrafica?.indirizzo?.numeroCivico;
        ad.Dug__c = anagrafica?.indirizzo?.dug;
        ad.City__c = anagrafica?.indirizzo?.localita;
        ad.ShortCity__c = anagrafica?.indirizzo?.localitaBreve;
        ad.CadastralCode__c = anagrafica?.indirizzo?.codiceBelfioreComune;
        ad.PostalCode__c = anagrafica?.indirizzo?.codicePostale;
        ad.IstatCode__c = anagrafica?.indirizzo?.codiceIstatComune;
        ad.State__c = anagrafica?.indirizzo?.abbreviazioneProvincia;
        ad.IstatCodeAnag1__c = anagrafica?.indirizzo?.codiceIstatAnag1;
        ad.AddressType__c = anagrafica?.indirizzo?.tipoIndirizzo;
        ad.CadestralCountryCode__c = anagrafica?.indirizzo?.codiceBelfioreStato;
        ad.AddressSourceSystemIdentifier__c = (anagrafica?.indirizzo?.id != null) ? String.valueOf(anagrafica?.indirizzo?.id) : null;

        //Privacy
        ad.SourceSystemConsentCode__c = input?.privacy?.datiAdesione?.tipoPrivacy;
        //ad.SourceSystemConsentEffectiveDate__c = input?.privacy?.datiAdesione?.SourceSystemConsentEffectiveDate__c;
        //ad.SourceSystemConsentEndDate__c = input?.privacy?.datiAdesione?.SourceSystemConsentEndDate__c;
        //Optout
        ad.OptOutStartApplication__c = input?.privacy?.datiOptOut?.applicazioneFine;
        ad.OptOutEndApplication__c = input?.privacy?.datiOptOut?.applicazioneInizio;
        ad.OptOutType__c = input?.privacy?.datiOptOut?.tipoOptOut;
        ad.OptOutEffectiveDate__c = (input?.privacy?.datiOptOut?.dataInizioEffetto != null) ? Date.valueOf(input?.privacy?.datiOptOut?.dataInizioEffetto) : null;
        ad.OptOutEndDate__c = (input?.privacy?.datiOptOut?.dataFineEffetto != null) ? Date.valueOf(input?.privacy?.datiOptOut?.dataFineEffetto) : null;

        //ad.BirthDate__c = Date.valueOf(anagrafica.dataNascita);
        //ad.BirthCountry__c = anagrafica.nazioneNascita;

        Schema.SObjectField adExtId = AccountDetails__c.ExternalId__c.getDescribe().getSObjectField();
        Database.UpsertResult srAd = Database.upsert(ad, adExtId, false);
        if(!srAd.isSuccess()){
            for(Database.Error err : srAd.getErrors()){
                System.debug('Error ad creation: ' + err.getMessage());
            }
            if(String.valueOf(srAd.getErrors()[0].getStatusCode()) != 'DUPLICATE_VALUE'){
                Database.rollback(sp);
                return JSON.serialize(new respWrapper(null, false, 'Error ad creation: ' + srAd.getErrors()[0].getMessage()));
            }
        }

        ad = [SELECT Id, Relation__c, SourceSystemIdentifier__c, Name, ExternalId__c, VatNumber__c, FullName__c, SourceSystemType__c, ProfessionCode__c, ProfessionLegalEntityType__c, CompanyType__c, SourceSystemRegistryType__c, SourceSystemStatus__c, VatNumberStatus__c, FiscalCodeStatus__c, CensusDistrict__c, Latitude__c, Longitude__c, At__c, Street__c, AddressNormalizationType__c, Dus__c, StreetNumber__c, Dug__c, City__c, ShortCity__c, CadastralCode__c, PostalCode__c, IstatCode__c, State__c, IstatCodeAnag1__c, AddressType__c, CadestralCountryCode__c FROM AccountDetails__c WHERE ExternalId__c =: ('ANAG2_' + anagrafica.partitaIVA + '_SOC_' + companyCodeRaw) LIMIT 1];

        System.debug('###AdCreateUpsert: ' + srAd.isCreated());
        System.debug('##ad' + ad);

        String resp = JSON.serialize(new respWrapper(accId, aarAgency.Id, aarCompany.Id, aad.Id, ad.Id, ad.SourceSystemIdentifier__c, String.valueOf(Userinfo.getUserId()), true, null));
        System.debug('##resp##: ' + resp);

        return resp;
    }

    @AuraEnabled
    public static Map<String, Object> userHasMultipleMandati(){
        Map<String, Object> returnValues = new Map<String, Object>{
            'hasMultipleMandati' => false,
            'error' => false
        };
        try{
            List<PermissionSetAssignment> mandati = [SELECT Id FROM PermissionSetAssignment WHERE AssigneeId =: UserInfo.getUserId() AND (PermissionSet.Name = 'MandatoUnipolSai' OR PermissionSet.Name = 'MandatoUnisalute')];
            returnValues.put('hasMultipleMandati', mandati.size() > 1);
        } catch (Exception e) {
            returnValues.put('error', true);
            returnValues.put('errorMessage', e.getMessage());
            returnValues.put('stackTraceString', e.getStackTraceString());
        }
        return returnValues;
    }

    //Wrapper definition
    public virtual class RootNoProf {

        public Anagrafica anagrafica { get; set; }
        public DatiAgenzia datiAgenzia { get; set; }
        public Privacy privacy { get; set; }
        public List<Contatto> contatti { get; set; }
        //public Professione professione { get; set; }

        public virtual Professione getProfessione() {
            return null;
        }
        public virtual List<AltriIndirizzi> getAltriIndirizzi() {
            return null;
        }
    }
    public class RootProf extends RootNoProf{

        public Professione professione { get; set; }
        public override Professione getProfessione() {
            return this.professione;
        }
    }

    public class RootProfPF extends RootNoProf{

        public List<AltriIndirizzi> altriIndirizzi { get; set; }
        public Professione professione { get; set; }
        public override Professione getProfessione() {
            return this.professione;
        }
        public override List<AltriIndirizzi> getAltriIndirizzi() {
            return this.altriIndirizzi;
        }
    }

    public class RootNoProfPF extends RootNoProf{

        public List<AltriIndirizzi> altriIndirizzi { get; set; }
        //public Professione professione { get; set; }
        /*public override Professione getProfessione() {
            return this.professione;
        }*/
        public override List<AltriIndirizzi> getAltriIndirizzi() {
            return this.altriIndirizzi;
        }
    }


    public class Anagrafica {

        public Integer ciu { get; set; }
        public String codiceFiscale { get; set; }
        public String cognome { get; set; }
        public String nome { get; set; }
        public String sesso { get; set; }
        public String tipoSoggetto { get; set; }
        public String tipoAnagrafica { get; set; }
        public String dataNascita { get; set; }
        public String nazioneNascita { get; set; }
        public String provinciaNascita { get; set; }
        public String belfioreComuneNascita { get; set; }
        public String statoCodiceFiscale { get; set; }
        public String statoPartitaIva { get; set; }
        public String tipoFormaSocietaria { get; set; }
        public Boolean flagPartitaIVACertificataCRIBIS { get; set; }
        public String statoAnagrafica { get; set; }
        public String chiaveMigrazione { get; set; }
        public DatiTracciatura datiTracciatura { get; set; }
        public Indirizzo indirizzo { get; set; }
        public String partitaIVA { get; set; }
        public String ragioneSociale { get; set; }
    }
    public class DatiTracciatura {

        public String dataCreazione { get; set; }
        public String userIdCreazione { get; set; }
        public String usernameCreazione { get; set; }
        public String canaleCreazione { get; set; }
        public String compagniaCreazione { get; set; }
        public String sistemaCreazione { get; set; }
        public String sottosistemaCreazione { get; set; }
        public String applicazioneChiamante { get; set; }
        public String proceduraChiamante { get; set; }
    }

    public class Indirizzo {

        public Integer id { get; set; }
        public String abbreviazioneProvincia { get; set; }
        public String cabComune { get; set; }
        public String cabStato { get; set; }
        public String codiceBelfioreComune { get; set; }
        public String codiceBelfioreStato { get; set; }
        public String codiceIstatComune { get; set; }
        public String codiceIstatAnag1 { get; set; }
        public String codiceUnsdM49 { get; set; }
        public String codicePostale { get; set; }
        public String dug { get; set; }
        public String dus { get; set; }
        public String tipoNormalizzato { get; set; }
        public String indirizzoCompleto { get; set; }
        public String localita { get; set; }
        public String presso { get; set; }
        public String numeroCivico { get; set; }
        public String tipoIndirizzo { get; set; }
        public String indirizzoBreve { get; set; }
        public Double latitudine { get; set; }
        public Double longitudine { get; set; }
        public String distrettoCensuario { get; set; }
        public Boolean flagPreview { get; set; }
        public Integer ciu { get; set; }
        public String compagnia { get; set; }
        public DatiTracciatura datiTracciatura { get; set; }
        public String localitaBreve { get; set; }
    }

    public class AltriIndirizzi {

        public Integer id { get; set; }
        public String abbreviazioneProvincia { get; set; }
        public String cabComune { get; set; }
        public String cabStato { get; set; }
        public String codiceBelfioreComune { get; set; }
        public String codiceBelfioreStato { get; set; }
        public String codiceIstatComune { get; set; }
        public String codiceIstatAnag1 { get; set; }
        public String codiceUnsdM49 { get; set; }
        public String codicePostale { get; set; }
        public String dug { get; set; }
        public String dus { get; set; }
        public String tipoNormalizzato { get; set; }
        public String indirizzoCompleto { get; set; }
        public String localita { get; set; }
        public String presso { get; set; }
        public String numeroCivico { get; set; }
        public String tipoIndirizzo { get; set; }
        public String indirizzoBreve { get; set; }
        public Double latitudine { get; set; }
        public Double longitudine { get; set; }
        public String distrettoCensuario { get; set; }
        public Boolean flagPreview { get; set; }
        public Integer ciu { get; set; }
        public String compagnia { get; set; }
        public DatiTracciatura datiTracciatura { get; set; }
        public String localitaBreve { get; set; }
    }

    public class DatiAgenzia {

        public Integer ciu { get; set; }
        public String codiceAgenzia { get; set; }
        public String codiceAgenziaPrevalente { get; set; }
        public String codiceCanale { get; set; }
        public String codiceSubagenzia { get; set; }
        public String compagnia { get; set; }
        public String dataCessazioneCliente { get; set; }
        public String dataClienteTop { get; set; }
        public String dataFineEffetto { get; set; }
        public String dataInizioEffetto { get; set; }
        public Boolean flagAdesioneFEA { get; set; }
        public Boolean flagAutorizzazioneCauzione { get; set; }
        public Boolean flagClienteTop { get; set; }
        public Boolean flagProprietaContattiFea { get; set; }
        public Integer idDatiAnagraficiAgenzia { get; set; }
        public String codiceSoggettoCanale { get; set; }
        public String statoSoggetto { get; set; }
        public DatiTracciatura datiTracciatura { get; set; }
    }

    public class Privacy {

        public DatiAdesione datiAdesione { get; set; }
        public DatiOptOut datiOptOut { get; set; }
        public Object logStampa { get; set; }
        public Object contattoAggiuntivo { get; set; }
        public Object errore { get; set; }
    }

    public class DatiOptOut {

        public DatiTracciatura datiTracciatura { get; set; }
        public Integer id { get; set; }
        public String compagnia { get; set; }
        public Integer ciu { get; set; }
        public String dataInizioEffetto { get; set; }
        public String dataFineEffetto { get; set; }
        public String tipoOptOut { get; set; }
        public String applicazioneInizio { get; set; }
        public String applicazioneFine { get; set; }         
    }

    public class DatiAdesione {

        public Integer id { get; set; }
        public String compagnia { get; set; }
        public Integer ciu { get; set; }
        public String dataInizioEffetto { get; set; }
        public String dataFineEffetto { get; set; }
        public String tipoPrivacy { get; set; }
        public String applicazioneInizio { get; set; }
        public String applicazioneFine { get; set; }
        public DatiTracciatura datiTracciatura { get; set; }
    }

 

    public class Contatto {

        public String compagnia { get; set; }
        public String agenzia { get; set; }
        public Integer ciu { get; set; }
        public Integer id { get; set; }
        public String tipoContatto { get; set; }
        public String tipologiaContatto { get; set; }
        public String contatto { get; set; }
        public String referente { get; set; }
        public Boolean flagPreferito { get; set; }
        public DatiTracciatura datiTracciatura { get; set; }
    }

    public class Professione {

        public ProfessioneDettaglio professione { get; set; }
        public Object valoriSelezionabili { get; set; }
        public Impiego impiego { get; set; }
        public Settore settore { get; set; }
        public Specializzazione specializzazione { get; set; }
        public PersonaGiuridica personaGiuridica { get; set; }
        public MercatoPreferenziale mercatoPreferenziale { get; set; }
    }

    public class ProfessioneDettaglio {

        public String codice { get; set; }
        public String descrizione { get; set; }
    }

    public class Impiego {

        public String codice { get; set; }
        public String descrizione { get; set; }
    }

    public class Settore {

        public String codice { get; set; }
        public String descrizione { get; set; }
    }

    public class MercatoPreferenziale {

        public String codice { get; set; }
        public String descrizione { get; set; }
    }

    public class Specializzazione {

        public String codice { get; set; }
        public String descrizione { get; set; }
    }

    public class PersonaGiuridica {

        public String codice { get; set; }
        public String descrizione { get; set; }
    }
}