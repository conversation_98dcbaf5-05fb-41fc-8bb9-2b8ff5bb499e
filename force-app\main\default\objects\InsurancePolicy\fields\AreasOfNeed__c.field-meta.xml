<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>AreasOfNeed__c</fullName>
    <label>Areas Of Need</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <type>MultiselectPicklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Veicoli</fullName>
                <default>false</default>
                <label>Veicoli</label>
            </value>
            <value>
                <fullName>Casa</fullName>
                <default>false</default>
                <label>Casa</label>
            </value>
            <value>
                <fullName>Cane e Gatto</fullName>
                <default>false</default>
                <label>Cane e Gatto</label>
            </value>
            <value>
                <fullName>Famiglia</fullName>
                <default>false</default>
                <label>Famiglia</label>
            </value>
            <value>
                <fullName>Infortuni</fullName>
                <default>false</default>
                <label>Infortuni</label>
            </value>
            <value>
                <fullName>Mobilita</fullName>
                <default>false</default>
                <label>Mobilità</label>
            </value>
            <value>
                <fullName>Salute</fullName>
                <default>false</default>
                <label>Salute</label>
            </value>
            <value>
                <fullName>Viaggio</fullName>
                <default>false</default>
                <label>Viaggio</label>
            </value>
            <value>
                <fullName>Vita</fullName>
                <default>false</default>
                <label>Vita</label>
            </value>
            <value>
                <fullName>Casa e Famiglia</fullName>
                <default>false</default>
                <label>Casa e Famiglia</label>
            </value>
            <value>
                <fullName>Persona</fullName>
                <default>false</default>
                <label>Persona</label>
            </value>
        </valueSetDefinition>
    </valueSet>
    <visibleLines>4</visibleLines>
</CustomField>
