<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>AreaOfNeed__c</fullName>
    <description>Ambito di protezione</description>
    <label>Area of Need</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Cane e Gatto</fullName>
                <default>false</default>
                <label>Cane e Gatto</label>
            </value>
            <value>
                <fullName>Casa</fullName>
                <default>false</default>
                <label>Casa</label>
            </value>
            <value>
                <fullName>Famiglia</fullName>
                <default>false</default>
                <label>Famiglia</label>
            </value>
            <value>
                <fullName>Infortuni</fullName>
                <default>false</default>
                <label>Infortuni</label>
            </value>
            <value>
                <fullName>Mobilità</fullName>
                <default>false</default>
                <label>Mobilità</label>
            </value>
            <value>
                <fullName>Salute</fullName>
                <default>false</default>
                <label>Salute</label>
            </value>
            <value>
                <fullName>Veicoli</fullName>
                <default>false</default>
                <label>Veicoli</label>
            </value>
            <value>
                <fullName>Viaggio</fullName>
                <default>false</default>
                <label>Viaggio</label>
            </value>
            <value>
                <fullName>Previdenza integrativa</fullName>
                <default>false</default>
                <label>Previdenza integrativa</label>
            </value>
            <value>
                <fullName>Vita</fullName>
                <default>false</default>
                <label>Vita</label>
            </value>
            <value>
                <fullName>Casa e Famiglia</fullName>
                <default>false</default>
                <label>Casa e Famiglia</label>
            </value>
            <value>
                <fullName>Persona</fullName>
                <default>false</default>
                <label>Persona</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
