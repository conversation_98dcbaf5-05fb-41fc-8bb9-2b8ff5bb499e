<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>SetPayloadREProdotti</name>
        <label>SetPayloadREProdotti</label>
        <locationX>930</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>payloadREProdotti</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>SetPayloadUnicaVetrinaProdotti</name>
        <label>SetPayloadUnicaVetrinaProdotti</label>
        <locationX>50</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>payloadUnicaVetrinaProdotti</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>SetPayloadUSNextNuovoPreventivo</name>
        <label>SetPayloadUSNextNuovoPreventivo</label>
        <locationX>1370</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>payloadUSNextVetrinaProdotti</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>SetPayloadVitaProdotti</name>
        <label>SetPayloadVitaProdotti</label>
        <locationX>490</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>payloadVitaProdotti</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Check_RE</name>
        <label>Check RE</label>
        <locationX>1062</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>FeiContainerScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>RE_PRODOTTI</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RE.PRODOTTI</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>SetPayloadREProdotti</targetReference>
            </connector>
            <label>RE.PRODOTTI</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_UNICA</name>
        <label>Check UNICA</label>
        <locationX>182</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>FeiContainerScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>UNICA_VETRINA_PRODOTTI</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>UNICA.VETRINA.PRODOTTI</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>SetPayloadUnicaVetrinaProdotti</targetReference>
            </connector>
            <label>UNICA.VETRINA.PRODOTTI</label>
        </rules>
    </decisions>
    <decisions>
        <name>CHECK_USNEXT</name>
        <label>CHECK USNEXT</label>
        <locationX>1502</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>FeiContainerScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>USNEXT_NUOVO_PREVENTIVO</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>USNEXT.NUOVO.PREVENTIVO</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>SetPayloadUSNextNuovoPreventivo</targetReference>
            </connector>
            <label>USNEXT.NUOVO.PREVENTIVO</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_VITA</name>
        <label>Check VITA</label>
        <locationX>622</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>FeiContainerScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>VITA_PRODOTTI</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>VITA.PRODOTTI</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>SetPayloadVitaProdotti</targetReference>
            </connector>
            <label>VITA.PRODOTTI</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckType</name>
        <label>CheckType</label>
        <locationX>996</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>FeiContainerScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>UNICA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>UNICA.</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_UNICA</targetReference>
            </connector>
            <label>UNICA</label>
        </rules>
        <rules>
            <name>VITA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>VITA.</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_VITA</targetReference>
            </connector>
            <label>VITA</label>
        </rules>
        <rules>
            <name>RE</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>RE.</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_RE</targetReference>
            </connector>
            <label>RE</label>
        </rules>
        <rules>
            <name>USNEXT</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>USNEXT.</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CHECK_USNEXT</targetReference>
            </connector>
            <label>USNEXT</label>
        </rules>
    </decisions>
    <description>Aggiunto ramo per UniSalute Next</description>
    <environments>Default</environments>
    <formulas>
        <name>payloadREProdotti</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;id&quot;:&quot;&apos;&amp;{!customerID}&amp;&apos;&quot;}&apos;</expression>
    </formulas>
    <formulas>
        <name>payloadUnicaVetrinaProdotti</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;cf&quot;:&quot;&apos;&amp;{!customerID}&amp;&apos;&quot;}&apos;</expression>
    </formulas>
    <formulas>
        <name>payloadUSNextVetrinaProdotti</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;codfisc&quot;:&quot;&apos;&amp;{!customerID}&amp;&apos;&quot;}&apos;</expression>
    </formulas>
    <formulas>
        <name>payloadVitaProdotti</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;id&quot;:&quot;&apos;&amp;{!customerID}&amp;&apos;&quot;}&apos;</expression>
    </formulas>
    <interviewLabel>FEIQuickActionVetrinaProdotti {!$Flow.CurrentDateTime}</interviewLabel>
    <label>FEIQuickActionVetrinaProdotti</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>GetCurrentFEISetting</name>
        <label>GetCurrentFEISetting</label>
        <locationX>996</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>CheckType</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Label</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>FEIID</elementReference>
            </value>
        </filters>
        <filters>
            <field>Environment__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>environment</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>FEI_Settings__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>FeiContainerScreen</name>
        <label>FeiContainerScreen</label>
        <locationX>996</locationX>
        <locationY>734</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>FeiContainer</name>
            <extensionName>c:feiContainer</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>recordId</name>
                <value>
                    <elementReference>recordId</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>feiRequestPayload</name>
                <value>
                    <elementReference>feiRequestPayload</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>FEIID</name>
                <value>
                    <elementReference>FEIID</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>FiscalCode</name>
                <value>
                    <elementReference>$User.FederationIdentifier</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>permissionSetName</name>
                <value>
                    <elementReference>GetCurrentFEISetting.UCA_Permission_Name__c</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>feiAddressEndpointPayload</name>
                <value>
                    <elementReference>feiAddressEndpointPayload</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>cfcontid</name>
                <value>
                    <elementReference>customerID</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>society</name>
                <value>
                    <elementReference>society</elementReference>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>870</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetCurrentFEISetting</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>customerID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>environment</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <elementReference>$Setup.FEI_Environment__c.Environment__c</elementReference>
        </value>
    </variables>
    <variables>
        <name>feiAddressEndpointPayload</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>FEIID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>feiRequestPayload</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>society</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
