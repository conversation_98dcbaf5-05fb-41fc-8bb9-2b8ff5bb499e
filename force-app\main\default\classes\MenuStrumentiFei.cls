public without sharing class MenuStrumentiFei {
    
    public static String getFeiParams(string feiid, string userContext) {
        if(feiid == null) {
            return feiid;
        }

        String cf = [SELECT FederationIdentifier FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1].FederationIdentifier;

        Map<String, Object> mapObj = new Map<String, Object>();
        String jsonParam = null;
        if(feiid == 'PREVENTIVATORE.UNISALUTE') {

            String externalId = null;
            if(userContext != null) {
                NetworkUser__c ne = [SELECT Id, NetworkUser__c, Agency__c FROM NetworkUser__c WHERE NetworkUser__c = :userContext LIMIT 1];
                
                //String azienda = [SELECT IdAzienda__c FROM User WHERE Id = :UserInfo.getUserId()].IdAzienda__c;
                if(ne != null) {
                    try {
                        Account acc = [SELECT ExternalId__c FROM Account WHERE Id = :ne.Agency__c];
                        
                        if(acc != null && acc.ExternalId__c != null) {
                            externalId = acc.ExternalId__c.replace('AGE_', '');
                        }
                    } catch(Exception e) {
                        System.debug(e);
                    }
                }
            }

            if(userContext != null) {
                mapObj.put('userId', userContext);
            }
            if(externalId != null) {
                mapObj.put('agem', externalId);
                mapObj.put('agef', externalId);
            }
            if(cf != null) {
                mapObj.put('ope', cf);
            }
            mapObj.put('hash', '6b6ee1d0dd0aa410fc8375faa44ba22b288be78fbfefadcbc873c4238d3f9da1');

            jsonParam = JSON.serialize(mapObj);
        }

        return jsonParam;
    }
}