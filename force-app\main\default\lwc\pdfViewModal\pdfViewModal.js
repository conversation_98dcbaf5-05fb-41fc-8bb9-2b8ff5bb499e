import { LightningElement, api, track } from 'lwc';
import getPDFDocument from '@salesforce/apex/AccordionController.getPDFDocument';
import deletePDFDocument from '@salesforce/apex/AccordionController.deletePDFDocument';

export default class PdfViewModal extends LightningElement {
    @api url = '';
    @api showModal = false;
    @api title = 'Anteprima PDF';
    @track urlPDF = '';
    @track distributionId = '';
    @track errorMsgUrl = '';
    @api domainType;

    renderedCallback() {
        // Mostra solo la prima volta con URL valido e senza distribuzione già creata
        if (this.showModal && this.url && !this.distributionId) {
            // Mostra loader del componente c-loader
            const loader = this.template.querySelector('c-loader');
            if (loader) loader.showLoader();

            // Calcola ipType senza modificare domainType
            const ipType = this.domainType === 'ESSIG_VITA_PREVIDENZA'
                ? 'RestPost_PDFDocument_Previdenza'
                : 'RestPost_PDFDocument';
            console.log('url '+this.url+' integration procedure: '+ipType);
            getPDFDocument({
                ipInput: { url: this.url, xmlBody: this.url },
                ipType: ipType
            })
            .then(response => {
                if (!response.error && response.contentDistribution) {
                    this.distributionId = response.contentDistribution.Id;
                    this.urlPDF = response.contentDistribution.DistributionPublicUrl;
                } else {
                    this.errorMsgUrl = response.errMsg;
                    console.error('Errore getPDFDocument:', response.errMsg);
                }
            })
            .catch(err => {
                console.error('Errore chiamata Apex getPDFDocument:', err);
                this.errorMsgUrl = err?.body?.message || JSON.stringify(err);
            })
            .finally(() => {
                if (loader) loader.hideLoader();
            });
        }
    }

    handleClose() {
        // Elimina distribuzione se esiste
        if (this.distributionId) {
            deletePDFDocument({ distributionId: this.distributionId })
            .then(() => {
                this._reset();
                this._dispatchClose();
            })
            .catch(err => {
                console.error('Errore deletePDFDocument:', err);
                this._dispatchClose();
            });
        } else {
            this._dispatchClose();
        }
    }

    downloadPdf() {
        if (!this.urlPDF) return;
        const link = document.createElement('a');
        link.href = this.urlPDF;
        link.download = 'result.pdf';
        link.target = '_blank';
        link.click();
    }

    get isPdfExisting() {
        return !!this.urlPDF;
    }

    get frameWidth() {
        return window.innerWidth - (window.innerWidth / 10);
    }

    get frameHeight() {
        return window.innerHeight - (window.innerHeight / 10);
    }

    get errorGetUrl() {
        return this.errorMsgUrl !== '';
    }

    /**
     * Disable download button when error or no PDF
     */
    get isDownloadDisabled() {
        return this.errorGetUrl || !this.isPdfExisting;
    }

    _dispatchClose() {
        this.showModal = false;
        this.dispatchEvent(new CustomEvent('close'));
    }

    _reset() {
        this.distributionId = '';
        this.urlPDF = '';
        this.errorMsgUrl = '';
    }
}