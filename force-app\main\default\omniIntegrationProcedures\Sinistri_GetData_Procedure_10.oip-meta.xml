<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{
    &quot;recordId&quot;: &quot;0019X000017xaSSQAY&quot;,
    &quot;userId&quot;: &quot;0059X00000Jr08cQAB&quot;
}</customJavaScript>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>true</isActive>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>Procedure</language>
    <name>Uni_Sinistri/GetData</name>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>AddUrl</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;HTTPAction&quot; : &quot;%HTTPAction%&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;SinistriHandler&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;remoteMethod&quot; : &quot;createUrlSinistri&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;RemoteAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>9.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetAccount</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;uniGetAccountSinistriNew&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;idToSearch&quot;,
    &quot;element&quot; : &quot;recordId&quot;
  }, {
    &quot;inputParam&quot; : &quot;UserId&quot;,
    &quot;element&quot; : &quot;userId&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>HTTPActionGetSinistri</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;varIsDev:isDev!=true&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/sinistri&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;retryCount&quot; : 0,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;params&quot; : {
      &quot;codiceFiscale&quot; : &quot;%Request:codiceFiscale%&quot;,
      &quot;userId&quot; : &quot;%Request:userId%&quot;,
      &quot;codiceCompagnia&quot; : &quot;%Request:codiceCompagnia%&quot;,
      &quot;codiceAgenzia&quot; : &quot;%Request:codiceAgenzia%&quot;
    },
    &quot;timeout&quot; : 0,
    &quot;sendBody&quot; : true,
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;isCompressed&quot; : false
  },
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;HTTPAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>6.0</sequenceNumber>
        <type>Rest Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetValues2</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;url&quot; : &quot;=CONCAT(\&quot;/flow/FEIQuickActionSinistri?FEIID=SXCC.INTERROGAZIONE&amp;numeroSinistro=\&quot;, %HTTPAction:compagniaSinistro%,\&quot;-\&quot;,%HTTPAction:agenziaSinistro%,\&quot;-\&quot;,%HTTPAction:esercizioSinistro%,\&quot;-\&quot;,%HTTPAction:numeroSinistro%)&quot;
  },
  &quot;responseJSONPath&quot; : &quot;url&quot;,
  &quot;responseJSONNode&quot; : &quot;HTTPAction:url&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>LoopBlock1</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;loopList&quot; : &quot;HTTPAction&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;loopOutput&quot; : {
    &quot;HTTPAction&quot; : &quot;%HTTPAction%&quot;
  },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;LoopBlock1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>7.0</sequenceNumber>
        <type>Loop Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>NetworkUserExtract</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;NetworkUserExtract&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;element&quot; : &quot;userId&quot;,
    &quot;inputParam&quot; : &quot;UserId&quot;
  }, {
    &quot;element&quot; : &quot;&apos;SOC_1&apos;&quot;,
    &quot;inputParam&quot; : &quot;societa&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;disOnTplt&quot; : false,
  &quot;label&quot; : &quot;DataMapperExtractAction2&quot;,
  &quot;show&quot; : null
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Request</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;userId&quot; : &quot;=%NetworkUserExtract:NetUser%&quot;,
    &quot;codiceCompagnia&quot; : &quot;=%GetAccount:Account:societa%&quot;,
    &quot;codiceAgenzia&quot; : &quot;=%GetAccount:Account:codiceAgenzia%&quot;,
    &quot;codiceFiscale&quot; : &quot;=%GetAccount:Account:codiceFiscale%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ResponseAction1</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;contatoreGenerale&quot; : &quot;=%contatoriValues:contatoreGenerale%&quot;,
    &quot;contatoreAperti&quot; : &quot;=%contatoriValues:contatoreAperti%&quot;,
    &quot;contatoreChiusi&quot; : &quot;=%contatoriValues:contatoreChiusi%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;ListDef&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>10.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetValues3</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;List&quot; : &quot;=LoopBlock1:HTTPAction&quot;
  },
  &quot;responseJSONPath&quot; : &quot;List&quot;,
  &quot;responseJSONNode&quot; : &quot;ListDef:Lista&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues3&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>8.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>varIsDev</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;isDev&quot; : &quot;=false&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues4&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>varResponseMock</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;varIsDev:isDev=true&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;responseMock&quot; : &quot;=DESERIALIZE(&apos;[    {       \&quot;NSS\&quot;:true,       \&quot;agenziaPolizza\&quot;:\&quot;1853\&quot;,       \&quot;agenziaSinistro\&quot;:\&quot;8101\&quot;,       \&quot;compagniaPolizza\&quot;:\&quot;1\&quot;,       \&quot;compagniaSinistro\&quot;:\&quot;1\&quot;,       \&quot;dataDenuncia\&quot;:\&quot;2024-03-24T23:00:00Z\&quot;,       \&quot;dataEvento\&quot;:\&quot;2024-03-22T07:28:00Z\&quot;,       \&quot;dataSinistro\&quot;:\&quot;2024-03-22T07:28:00Z\&quot;,       \&quot;enteGestoreCodice\&quot;:\&quot;996\&quot;,       \&quot;enteGestoreDescrizione\&quot;:\&quot;SARC ID DEBITORI\&quot;,       \&quot;esercizioSinistro\&quot;:\&quot;2024\&quot;,       \&quot;numeroEvento\&quot;:\&quot;2024/585176\&quot;,       \&quot;numeroPolizza\&quot;:\&quot;167347191\&quot;,       \&quot;numeroSinistro\&quot;:\&quot;0227625\&quot;,       \&quot;pagato\&quot;:\&quot;0\&quot;,       \&quot;ramoPolizza\&quot;:\&quot;30\&quot;,       \&quot;ramoSinistroCodice\&quot;:\&quot;030\&quot;,       \&quot;ramoSinistroDescrizione\&quot;:\&quot;RCA\&quot;,       \&quot;statoSinistroCodice\&quot;:\&quot;senza_seguito\&quot;,       \&quot;statoSinistroDescrizione\&quot;:\&quot;Senza seguito\&quot;,       \&quot;subAgenziaPolizza\&quot;:\&quot;800\&quot;,       \&quot;targaSinistro\&quot;:\&quot;FN286RP\&quot;,       \&quot;url\&quot;:\&quot;/flow/FEIQuickActionSinistri?FEIID=SXCC.INTERROGAZIONE&amp;numeroSinistro=1-8101-2024-0227625\&quot;    },    {       \&quot;NSS\&quot;:true,       \&quot;agenziaPolizza\&quot;:\&quot;1853\&quot;,       \&quot;agenziaSinistro\&quot;:\&quot;8101\&quot;,       \&quot;compagniaPolizza\&quot;:\&quot;1\&quot;,       \&quot;compagniaSinistro\&quot;:\&quot;1\&quot;,       \&quot;dataDenuncia\&quot;:\&quot;2023-10-18T22:00:00Z\&quot;,       \&quot;dataEvento\&quot;:\&quot;2023-10-18T05:20:00Z\&quot;,       \&quot;dataSinistro\&quot;:\&quot;2023-10-18T05:20:00Z\&quot;,       \&quot;enteGestoreCodice\&quot;:\&quot;8867\&quot;,       \&quot;enteGestoreDescrizione\&quot;:\&quot;GESTIONE CANALIZZAZIONE UNIPOLSERVICE\&quot;,       \&quot;esercizioSinistro\&quot;:\&quot;2023\&quot;,       \&quot;numeroEvento\&quot;:\&quot;2023/2369546\&quot;,       \&quot;numeroPolizza\&quot;:\&quot;195422245\&quot;,       \&quot;numeroSinistro\&quot;:\&quot;952687\&quot;,       \&quot;pagato\&quot;:\&quot;1430.65\&quot;,       \&quot;ramoPolizza\&quot;:\&quot;30\&quot;,       \&quot;ramoSinistroCodice\&quot;:\&quot;030\&quot;,       \&quot;ramoSinistroDescrizione\&quot;:\&quot;RCA\&quot;,       \&quot;statoSinistroCodice\&quot;:\&quot;liquidato_totale\&quot;,       \&quot;statoSinistroDescrizione\&quot;:\&quot;Liquidato totale\&quot;,       \&quot;subAgenziaPolizza\&quot;:\&quot;800\&quot;,       \&quot;targaSinistro\&quot;:\&quot;GN545HM\&quot;,       \&quot;url\&quot;:\&quot;/flow/FEIQuickActionSinistri?FEIID=SXCC.INTERROGAZIONE&amp;numeroSinistro=1-8101-2023-952687\&quot;    },    {       \&quot;NSS\&quot;:true,       \&quot;agenziaPolizza\&quot;:\&quot;1853\&quot;,       \&quot;agenziaSinistro\&quot;:\&quot;8101\&quot;,       \&quot;compagniaPolizza\&quot;:\&quot;1\&quot;,       \&quot;compagniaSinistro\&quot;:\&quot;1\&quot;,       \&quot;dataDenuncia\&quot;:\&quot;2022-10-17T10:36:03Z\&quot;,       \&quot;dataEvento\&quot;:\&quot;2022-10-15T06:20:00Z\&quot;,       \&quot;dataSinistro\&quot;:\&quot;2022-10-15T06:20:00Z\&quot;,       \&quot;enteGestoreCodice\&quot;:\&quot;8867\&quot;,       \&quot;enteGestoreDescrizione\&quot;:\&quot;GESTIONE CANALIZZAZIONE UNIPOLSERVICE\&quot;,       \&quot;esercizioSinistro\&quot;:\&quot;2022\&quot;,       \&quot;numeroEvento\&quot;:\&quot;2022/2088133\&quot;,       \&quot;numeroPolizza\&quot;:\&quot;167347191\&quot;,       \&quot;numeroSinistro\&quot;:\&quot;0815594\&quot;,       \&quot;pagato\&quot;:\&quot;682.98\&quot;,       \&quot;ramoPolizza\&quot;:\&quot;30\&quot;,       \&quot;ramoSinistroCodice\&quot;:\&quot;030\&quot;,       \&quot;ramoSinistroDescrizione\&quot;:\&quot;RCA\&quot;,       \&quot;statoSinistroCodice\&quot;:\&quot;liquidato_totale\&quot;,       \&quot;statoSinistroDescrizione\&quot;:\&quot;Liquidato totale\&quot;,       \&quot;subAgenziaPolizza\&quot;:\&quot;800\&quot;,       \&quot;targaSinistro\&quot;:\&quot;FN286RP\&quot;,       \&quot;url\&quot;:\&quot;/flow/FEIQuickActionSinistri?FEIID=SXCC.INTERROGAZIONE&amp;numeroSinistro=1-8101-2022-0815594\&quot;    },    {       \&quot;NSS\&quot;:true,       \&quot;agenziaPolizza\&quot;:\&quot;1853\&quot;,       \&quot;agenziaSinistro\&quot;:\&quot;8001\&quot;,       \&quot;compagniaPolizza\&quot;:\&quot;1\&quot;,       \&quot;compagniaSinistro\&quot;:\&quot;1\&quot;,       \&quot;dataDenuncia\&quot;:\&quot;2022-03-14T23:00:00Z\&quot;,       \&quot;dataEvento\&quot;:\&quot;2022-03-09T23:00:00Z\&quot;,       \&quot;dataSinistro\&quot;:\&quot;2022-03-10T14:00:00Z\&quot;,       \&quot;enteGestoreCodice\&quot;:\&quot;857\&quot;,       \&quot;enteGestoreDescrizione\&quot;:\&quot;CALL CENTER SINISTRI\&quot;,       \&quot;esercizioSinistro\&quot;:\&quot;2022\&quot;,       \&quot;numeroEvento\&quot;:\&quot;2022/466472\&quot;,       \&quot;numeroPolizza\&quot;:\&quot;170174875\&quot;,       \&quot;numeroSinistro\&quot;:\&quot;0067980\&quot;,       \&quot;pagato\&quot;:\&quot;97.6\&quot;,       \&quot;ramoPolizza\&quot;:\&quot;30\&quot;,       \&quot;ramoSinistroCodice\&quot;:\&quot;016\&quot;,       \&quot;ramoSinistroDescrizione\&quot;:\&quot;Cristalli veicoli\&quot;,       \&quot;statoSinistroCodice\&quot;:\&quot;liquidato_totale\&quot;,       \&quot;statoSinistroDescrizione\&quot;:\&quot;Liquidato totale\&quot;,       \&quot;subAgenziaPolizza\&quot;:\&quot;800\&quot;,       \&quot;targaSinistro\&quot;:\&quot;FR868ZZ\&quot;,       \&quot;url\&quot;:\&quot;/flow/FEIQuickActionSinistri?FEIID=SXCC.INTERROGAZIONE&amp;numeroSinistro=1-8001-2022-0067980\&quot;    },    {       \&quot;NSS\&quot;:true,       \&quot;agenziaPolizza\&quot;:\&quot;1853\&quot;,       \&quot;agenziaSinistro\&quot;:\&quot;8001\&quot;,       \&quot;compagniaPolizza\&quot;:\&quot;1\&quot;,       \&quot;compagniaSinistro\&quot;:\&quot;1\&quot;,       \&quot;dataDenuncia\&quot;:\&quot;2022-03-11T23:00:00Z\&quot;,       \&quot;dataEvento\&quot;:\&quot;2022-02-27T23:00:00Z\&quot;,       \&quot;dataSinistro\&quot;:\&quot;2022-02-28T10:00:00Z\&quot;,       \&quot;enteGestoreCodice\&quot;:\&quot;857\&quot;,       \&quot;enteGestoreDescrizione\&quot;:\&quot;CALL CENTER SINISTRI\&quot;,       \&quot;esercizioSinistro\&quot;:\&quot;2022\&quot;,       \&quot;numeroEvento\&quot;:\&quot;2022/442015\&quot;,       \&quot;numeroPolizza\&quot;:\&quot;167347191\&quot;,       \&quot;numeroSinistro\&quot;:\&quot;0066274\&quot;,       \&quot;pagato\&quot;:\&quot;97.6\&quot;,       \&quot;ramoPolizza\&quot;:\&quot;30\&quot;,       \&quot;ramoSinistroCodice\&quot;:\&quot;016\&quot;,       \&quot;ramoSinistroDescrizione\&quot;:\&quot;Cristalli veicoli\&quot;,       \&quot;statoSinistroCodice\&quot;:\&quot;liquidato_totale\&quot;,       \&quot;statoSinistroDescrizione\&quot;:\&quot;Liquidato totale\&quot;,       \&quot;subAgenziaPolizza\&quot;:\&quot;800\&quot;,       \&quot;targaSinistro\&quot;:\&quot;FN286RP\&quot;,       \&quot;url\&quot;:\&quot;/flow/FEIQuickActionSinistri?FEIID=SXCC.INTERROGAZIONE&amp;numeroSinistro=1-8001-2022-0066274\&quot;    },    {       \&quot;NSS\&quot;:true,       \&quot;agenziaPolizza\&quot;:\&quot;1853\&quot;,       \&quot;agenziaSinistro\&quot;:\&quot;8101\&quot;,       \&quot;compagniaPolizza\&quot;:\&quot;1\&quot;,       \&quot;compagniaSinistro\&quot;:\&quot;1\&quot;,       \&quot;dataDenuncia\&quot;:\&quot;2019-11-27T14:14:27Z\&quot;,       \&quot;dataEvento\&quot;:\&quot;2019-11-24T17:40:00Z\&quot;,       \&quot;dataSinistro\&quot;:\&quot;2019-11-24T17:40:00Z\&quot;,       \&quot;enteGestoreCodice\&quot;:\&quot;8867\&quot;,       \&quot;enteGestoreDescrizione\&quot;:\&quot;GESTIONE CANALIZZAZIONE UNIPOLSERVICE\&quot;,       \&quot;esercizioSinistro\&quot;:\&quot;2019\&quot;,       \&quot;numeroEvento\&quot;:\&quot;2019/2393903\&quot;,       \&quot;numeroPolizza\&quot;:\&quot;170174875\&quot;,       \&quot;numeroSinistro\&quot;:\&quot;1007635\&quot;,       \&quot;pagato\&quot;:\&quot;719.95\&quot;,       \&quot;ramoPolizza\&quot;:\&quot;30\&quot;,       \&quot;ramoSinistroCodice\&quot;:\&quot;030\&quot;,       \&quot;ramoSinistroDescrizione\&quot;:\&quot;RCA\&quot;,       \&quot;statoSinistroCodice\&quot;:\&quot;liquidato_totale\&quot;,       \&quot;statoSinistroDescrizione\&quot;:\&quot;Liquidato totale\&quot;,       \&quot;subAgenziaPolizza\&quot;:\&quot;800\&quot;,       \&quot;targaSinistro\&quot;:\&quot;FR868ZZ\&quot;,       \&quot;url\&quot;:\&quot;/flow/FEIQuickActionSinistri?FEIID=SXCC.INTERROGAZIONE&amp;numeroSinistro=1-8101-2019-1007635\&quot;    },    {       \&quot;NSS\&quot;:true,       \&quot;agenziaPolizza\&quot;:\&quot;33139\&quot;,       \&quot;agenziaSinistro\&quot;:\&quot;8101\&quot;,       \&quot;compagniaPolizza\&quot;:\&quot;1\&quot;,       \&quot;compagniaSinistro\&quot;:\&quot;1\&quot;,       \&quot;dataDenuncia\&quot;:\&quot;2015-06-29T14:11:04Z\&quot;,       \&quot;dataEvento\&quot;:\&quot;2015-06-28T00:45:00Z\&quot;,       \&quot;dataSinistro\&quot;:\&quot;2015-06-28T00:45:00Z\&quot;,       \&quot;enteGestoreCodice\&quot;:\&quot;996\&quot;,       \&quot;enteGestoreDescrizione\&quot;:\&quot;SARC ID DEBITORI\&quot;,       \&quot;esercizioSinistro\&quot;:\&quot;2015\&quot;,       \&quot;numeroEvento\&quot;:\&quot;2015/470450\&quot;,       \&quot;numeroPolizza\&quot;:\&quot;116457162\&quot;,       \&quot;numeroSinistro\&quot;:\&quot;0234146\&quot;,       \&quot;pagato\&quot;:\&quot;0\&quot;,       \&quot;ramoPolizza\&quot;:\&quot;30\&quot;,       \&quot;ramoSinistroCodice\&quot;:\&quot;030\&quot;,       \&quot;ramoSinistroDescrizione\&quot;:\&quot;RCA\&quot;,       \&quot;statoSinistroCodice\&quot;:\&quot;senza_seguito\&quot;,       \&quot;statoSinistroDescrizione\&quot;:\&quot;Senza seguito\&quot;,       \&quot;targaSinistro\&quot;:\&quot;DT519DX\&quot;,       \&quot;url\&quot;:\&quot;/flow/FEIQuickActionSinistri?FEIID=SXCC.INTERROGAZIONE&amp;numeroSinistro=1-8101-2015-0234146\&quot;    } ]&apos;)&quot;
  },
  &quot;responseJSONPath&quot; : &quot;responseMock&quot;,
  &quot;responseJSONNode&quot; : &quot;HTTPAction&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues5&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessKey>Sinistri_GetData</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;linkToExternalObject&quot; : &quot;&quot;,
  &quot;trackingCustomData&quot; : { },
  &quot;includeAllActionsInResponse&quot; : false,
  &quot;columnsPropertyMap&quot; : [ ],
  &quot;relationshipFieldsMap&quot; : [ ],
  &quot;labelSingular&quot; : &quot;&quot;,
  &quot;labelPlural&quot; : &quot;&quot;,
  &quot;description&quot; : &quot;&quot;,
  &quot;nameColumn&quot; : &quot;&quot;,
  &quot;rollbackOnError&quot; : false,
  &quot;chainableQueriesLimit&quot; : 50,
  &quot;chainableDMLStatementsLimit&quot; : null,
  &quot;chainableCpuLimit&quot; : 2000,
  &quot;chainableHeapSizeLimit&quot; : null,
  &quot;chainableDMLRowsLimit&quot; : null,
  &quot;chainableQueryRowsLimit&quot; : null,
  &quot;chainableSoslQueriesLimit&quot; : null,
  &quot;chainableActualTimeLimit&quot; : null,
  &quot;additionalChainableResponse&quot; : { },
  &quot;queueableChainableQueriesLimit&quot; : 120,
  &quot;queueableChainableCpuLimit&quot; : 40000,
  &quot;queueableChainableHeapSizeLimit&quot; : 6,
  &quot;ttlMinutes&quot; : 5,
  &quot;mockResponseMap&quot; : { }
}</propertySetConfig>
    <subType>GetData</subType>
    <type>Sinistri</type>
    <uniqueName>Sinistri_GetData_Procedure_10</uniqueName>
    <versionNumber>10.0</versionNumber>
    <webComponentKey>34e45771-4578-9a21-7934-5af155737b50</webComponentKey>
</OmniIntegrationProcedure>
