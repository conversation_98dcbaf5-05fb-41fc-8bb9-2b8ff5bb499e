@isTest
public class AccountDetailsNPITriggerTest {
    @isTest
    static void testTriggerOnAfterInsert() {
        // Create test data
        Account agency = new Account(
            Name = 'Test Agency',
            RecordTypeId = [
                SELECT Id
                FROM RecordType
                WHERE DeveloperName = 'Agency' AND SObjectType = 'Account'
                LIMIT 1
            ].Id,
            ExternalId__c = 'AGE_12345'
        );
        insert agency;

        Account society = new Account(
            Name = 'Test Society',
            RecordTypeId = [
                SELECT Id
                FROM RecordType
                WHERE DeveloperName = 'Society' AND SObjectType = 'Account'
                LIMIT 1
            ].Id,
            ExternalId__c = 'SOC_12345'
        );
        insert society;

        // Crea un record di ruolo (supponendo che l'oggetto sia FinServ__ReciprocalRole__c)
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(
            Name = 'Agenzia',
            FinServ__InverseRole__c = 'Compagnia'
        );
        insert role;

        FinServ__AccountAccountRelation__c mandate = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = agency.Id,
            FinServ__RelatedAccount__c = society.Id,
            FinServ__Role__c = role.Id,
            RecordTypeId = Schema.SObjectType.FinServ__AccountAccountRelation__c.getRecordTypeInfosByDeveloperName()
                .get('AgencySociety')
                .getRecordTypeId()
        );
        insert mandate;

        AccountDetailsNPI__c accountDetailsNPI = new AccountDetailsNPI__c(
            Name = 'Test Account Details NPI',
            ExternalId__c = 'NPI_12345'
        );
        insert accountDetailsNPI;

        Test.startTest();

        // Insert AccountDetailsNPI__c record to trigger the logic
        AccountDetailsNPI__c newAccountDetailsNPI = new AccountDetailsNPI__c(
            Name = 'New Test Account Details NPI',
            ExternalId__c = 'NPI_67890'
        );
        insert newAccountDetailsNPI;

        Test.stopTest();

        // // Verify that the trigger executed successfully
        // List<AccountDetailsNPI__Share> shares = [
        //     SELECT Id, ParentId, UserOrGroupId, AccessLevel
        //     FROM AccountDetailsNPI__Share
        //     WHERE ParentId = :newAccountDetailsNPI.Id
        // ];
        // System.assertEquals(1, shares.size(), 'Expected one sharing record to be created.');
        // System.assertEquals('Read', shares[0].AccessLevel, 'Expected sharing access level to be Read.');
    }
}