/**
 * @File Name         : AccordionController.cls
 * @Description       :
 * <AUTHOR> <EMAIL>
 * @Group             :
 * @Last Modified On  : 30-11-2024
 * @Last Modified By  : <EMAIL>
@cicd_tests AccordionController_Test
**/
public without sharing class AccordionController {

    public class QuoteInfo {

        @AuraEnabled
        public String opportunityStage {get; set;} 
        
        @AuraEnabled
        public String opportunityContactChannel {get; set;} 

        @AuraEnabled
        public String cip {get; set;} 

        @AuraEnabled
        public String name {get; set;}

        @AuraEnabled
        public String recordId {get; set;}

        @AuraEnabled
        public List<String> areasOfNeed {get; set;}
        
        @AuraEnabled
        public String status {get; set;}

        @AuraEnabled
        public String source {get; set;}

        @AuraEnabled
        public String totalAmount {get; set;}

        @AuraEnabled
        public String creationDate {get; set;}

        @AuraEnabled
        public String expirationDate {get; set;}

        @AuraEnabled
        public String monthlyContribution {get; set;}

        @AuraEnabled
        public String documentUrl {get;set;}

        @AuraEnabled
        public String commercialStatus {get; set;}

        @AuraEnabled
        public String domainType {get; set;}

        @AuraEnabled
        public List<OpportunityCoverageInfo> opportunityCoverages {get; set;}

        @AuraEnabled public Boolean quoteIsStored   { get; set; }

        public QuoteInfo(String opportunityStage, String opportunityContactChannel, String cip, String name, String recordId, List<String> areasOfNeed, String status, String source, String totalAmount, String creationDate, String expirationDate, List<OpportunityCoverageInfo> opportunityCoverages, String monthlyContribution, String documentUrl, String commercialStatus, String domainType, Boolean quoteIsStored) { //NOPMD
            this.opportunityStage = opportunityStage;
            this.opportunityContactChannel = opportunityContactChannel;
            this.name = name;
            this.recordId = recordId;
            this.areasOfNeed = areasOfNeed;
            this.status = status;
            this.source = source;
            this.cip = cip;
            this.totalAmount = totalAmount;
            this.creationDate = creationDate;
            this.expirationDate = expirationDate;
            this.opportunityCoverages = opportunityCoverages;
            this.monthlyContribution = monthlyContribution;
            this.documentUrl = documentUrl;
            this.commercialStatus = commercialStatus;
            this.domainType = domainType;
            this.quoteIsStored = quoteIsStored;
        }
    }

    public class OpportunityCoverageInfo {
        @AuraEnabled
        public String areaOfNeed {get; set;}

        @AuraEnabled
        public String amount {get; set;}

        @AuraEnabled
        public List<String> assets {get; set;}

        @AuraEnabled
        public List<String> description {get; set;}
        
        @AuraEnabled
        public List<String> conventions {get; set;}

        @AuraEnabled
        public String fullName {get; set;}

        @AuraEnabled
        public List<String> fractionation {get; set;}

        @AuraEnabled
        public String stage {get; set;}

        @AuraEnabled
        public String monthlyContribution {get; set;}

        @AuraEnabled
        public String ral {get; set;}

        @AuraEnabled
        public String yearlyGrowth {get; set;}
        
        @AuraEnabled
        public String previdentialGap {get; set;}
        
        @AuraEnabled
        public String retirementYear {get; set;}
        
        @AuraEnabled
        public String targetProduct {get; set;}

        @AuraEnabled
        public String numOfChildren {get; set;}

        @AuraEnabled
        public String sector {get; set;}

        public OpportunityCoverageInfo()
        {
            System.debug('Creating coverage');
        }
    }
    
    /******************************************************************************************
    * @description  This method retrieves the list of Quote information associated with a given Opportunity record ID.
    * @param        recordId - ID of the Opportunity record
    * @return       List<QuoteInfo> - List of Quote information related to the Opportunity
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static List<QuoteInfo> getQuotesInfo(String recordId){
       try{
            Id opportunityId = Id.valueOf(recordId);
            List<Opportunity> opp = new List<Opportunity>();
            List<QuoteInfo> quotesInfo = new List<QuoteInfo>();
            System.debug('Retrieving quotes');
            opp = [ SELECT Id,StageName, ContactChannel__c, Channel__c, (SELECT Id, Name, Status, AreasOfNeed__c, EngagementPoint__c, QuoteAmount__c, CreatedDateTPD__c, CreatedDateFormula__c, ExpirationDate, MonthlyContribution__c, DocumentURL__c, toLabel(CommercialStatus__c),DomainType__c
                                FROM Quotes 
                                ORDER BY CreatedDateTPD__c DESC)
                    FROM Opportunity
                    WHERE Id =: opportunityId
                   	WITH SECURITY_ENFORCED
                    LIMIT 1
                ];

            if(!opp.isEmpty()){
                String opportunityStage = String.valueOf(opp.get(0).StageName);
                String opportunityContactChannel = String.valueOf(opp.get(0).ContactChannel__c);
                for(Quote q : opp.get(0).Quotes){
                    String quoteId = String.valueOf(q.Id);
                    List<String> areasOfNeed = getSelectedAreasInList(q);
                    String status = q.EngagementPoint__c;
                    String totalAmount = String.valueOf(q.QuoteAmount__c);
                    String creationDate = q.CreatedDateFormula__c;
                    String expirationDate = (q.ExpirationDate).format();
                    String monthlyContribution = String.valueOf(q.MonthlyContribution__c);
                    String documentUrl = q.DocumentURL__c;
                    String commercialStatus = q.CommercialStatus__c;
                    String domainType = q.DomainType__c ;
                    Boolean quoteIsStored = q.IsStored__c;
                    List<OpportunityCoverage__c> relatedOCs = getOpportunityCoverages(q.Id);
                    List<OpportunityCoverageInfo> ocInfo = getOpportunityCoveragesInfo(relatedOCs, opp.get(0).Channel__c);
                    QuoteInfo qInfo = new QuoteInfo(opportunityStage, opportunityContactChannel, '', q.Name, quoteId, areasOfNeed, status, '', totalAmount, creationDate, expirationDate, ocInfo, monthlyContribution, documentUrl, commercialStatus,domainType,quoteIsStored); 
                    quotesInfo.add(qInfo);
                }
                return quotesInfo;
            }
            return new List<QuoteInfo>{}; //No quotes, return empty list and n
        }
        catch(Exception e){
                String  errorMsg ='An error has occurred: ' + e.getMessage(); 
                throw new AuraHandledException(errorMsg);
        }
    }

    /******************************************************************************************
    * @description  This method retrieves the list of Quote information associated with a given Opportunity record ID, specifying whether Quote history is requested or not
    * @param        recordId - ID of the Opportunity record
    * @param        isStored - true if the Opportunity's Quote history is requested, false otherwise
    * @return       List<QuoteInfo> - List of Quote information related to the Opportunity
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static List<QuoteInfo> getStoredQuotesInfo(String recordId, Boolean isStored){
       try{
            Id opportunityId = Id.valueOf(recordId);
            List<Opportunity> opp = new List<Opportunity>();
            List<QuoteInfo> quotesInfo = new List<QuoteInfo>();

            opp = [ SELECT Id, StageName, ContactChannel__c, Channel__c, AssignedGroup__r.CIP__c, (SELECT Id, Name, Status, CIP__c, AreasOfNeed__c, EngagementPoint__c, QuoteAmount__c, CreatedDateTPD__c, CreatedDateFormula__c, ExpirationDate, MonthlyContribution__c, DocumentURL__c, toLabel(CommercialStatus__c),DomainType__c,IsStored__c
                                FROM Quotes
                                WHERE IsStored__c =: isStored
                                ORDER BY CreatedDateTPD__c DESC)
                    FROM Opportunity
                    WHERE Id =: opportunityId
                   	WITH SECURITY_ENFORCED
                    LIMIT 1
                ];

            if(!opp.isEmpty()){
                String opportunityStage = String.valueOf(opp.get(0).StageName);
                String opportunityContactChannel = String.valueOf(opp.get(0).ContactChannel__c);
                for(Quote q : opp.get(0).Quotes){
                    String cip = String.valueOf(q.CIP__c);
                    String quoteId = String.valueOf(q.Id);
                    List<String> areasOfNeed = getSelectedAreasInList(q);
                    String source = q.EngagementPoint__c;
                    String status = q.Status;
                    String totalAmount = String.valueOf(q.QuoteAmount__c);
                    String creationDate = String.valueOf(q.CreatedDateTPD__c.date().format());//FC: q.CreatedDateFormula__c; old value 05/05/2025
                    String expirationDate = (q.ExpirationDate).format();
                    String monthlyContribution = String.valueOf(q.MonthlyContribution__c);
                    String documentUrl = q.DocumentURL__c;
                    String commercialStatus = q.CommercialStatus__c;
                    String domainType = q.DomainType__c;
                    Boolean quoteIsStored = q.IsStored__c;
                    List<OpportunityCoverage__c> relatedOCs = getOpportunityCoverages(q.Id);
                    List<OpportunityCoverageInfo> ocInfo = getOpportunityCoveragesInfo(relatedOCs, opp.get(0).Channel__c);
                    QuoteInfo qInfo = new QuoteInfo(opportunityStage, opportunityContactChannel, cip, q.Name, quoteId, areasOfNeed, status, source, totalAmount, creationDate, expirationDate, ocInfo, monthlyContribution, documentUrl, commercialStatus,domainType,isStored); 
                    quotesInfo.add(qInfo);
                }
                return quotesInfo;
            }
            return new List<QuoteInfo>{}; //No quotes, return empty list and n
        }
        catch(Exception e){
                String  errorMsg ='An error has occurred: ' + e.getMessage(); 
                throw new AuraHandledException(errorMsg);
        }
    }

    /******************************************************************************************
    * @description  This method retrieves the list of OpportunityCoverage records associated with a given Quote record ID.
    * @param        quoteId - ID of the Quote record
    * @return       List<OpportunityCoverage__c> - List of OpportunityCoverage records related to the Quote
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static List<OpportunityCoverage__c> getOpportunityCoverages(Id quoteId){
        try{
            List<Quote> q = new List<Quote>();
            q = [   SELECT Id, (SELECT Id, AreaOfNeed__c, Description__c, Asset__c, Amount__c, FirstName__c, LastName__c, Conventions__c, Fractionation__c, EngagementPoint__c, RAL__c, MonthlyContribution__c, ProductOfInterest__c, PrevidentialGap__c, YearOfRetirement__c, ExpectedYearlyGrowth__c, NumberOfChildren__c, Sector__c  FROM OpportunityCoverages__r)
                    FROM Quote
                    WHERE Id =: quoteId 
                    WITH SECURITY_ENFORCED
                    LIMIT 1
                ];
            if(!q.isEmpty()){
                return q.get(0).OpportunityCoverages__r; //Retieve quotes of the opportunity
            }
            return new List<OpportunityCoverage__c>{}; //No quotes, return empty list and n
        }
        catch(Exception e){
                String  errorMsg ='An error has occurred: ' + e.getMessage(); 
                throw new AuraHandledException(errorMsg);
            }
    }

   /******************************************************************************************
    * @description  This method retrieves the list of selected areas of need from a given Quote record.
    * @param        q - Quote record
    * @return       List<String> - List of selected areas of need
    *******************************************************************************************/
    @TestVisible
    private static List<String> getSelectedAreasInList(Quote q){
        return new List<String>(q.AreasOfNeed__c?.split(';'));
    }

    /******************************************************************************************
    * @description  This method retrieves detailed information for a list of OpportunityCoverage records.
    * @param        relatedOCs - List of OpportunityCoverage records
    * @return       List<OpportunityCoverageInfo> - List of OpportunityCoverage information
    *******************************************************************************************/
    @TestVisible
    private static List<OpportunityCoverageInfo> getOpportunityCoveragesInfo(List<OpportunityCoverage__c> relatedOCs, String opportunityChannel) { //NOPMD
        List<OpportunityCoverageInfo> ocsInfo = new List<OpportunityCoverageInfo>();
        for(OpportunityCoverage__c oc : relatedOCs){
            OpportunityCoverageInfo ocInfo = new OpportunityCoverageInfo();
            if(opportunityChannel != 'Preventivatore Previdenza')
            {
                String amount = (oc.Amount__c != null) ? String.valueOf(oc.Amount__c) : '-';
                List<String> assets = (oc.Asset__c != null) ? formatedAssets(oc.Asset__c) : new List<String>{'-'};
                List<String> description = (oc.Description__c != null) ? formatedDescription(oc.Description__c) : new List<String>{'-'};
                List<String> conventions = (oc.Conventions__c != null) ? formatedConventions(oc.Conventions__c) : new List<String>{'-'};
                String firstName = (oc.FirstName__c != null) ? oc.FirstName__c : '-';
                String lastName = (oc.LastName__c != null) ? oc.LastName__c : '-';
                List<string> fractionation = (oc.Fractionation__c != null) ? formatedFractionation(oc.Fractionation__c) : new List<string>{'-'};
                String stage = (oc.EngagementPoint__c != null) ? oc.EngagementPoint__c : '-';
                
                ocInfo.areaOfNeed = oc.AreaOfNeed__c;
                ocInfo.amount = amount;
                ocInfo.assets = assets;
                ocInfo.description = description;
                ocInfo.conventions = conventions;
                ocInfo.fullName = fullName(firstName, lastName);
                ocInfo.fractionation = fractionation;
                ocInfo.stage = stage;
            }
            else
            {
                ocInfo.monthlyContribution = (oc.MonthlyContribution__c != null) ? String.valueOf(oc.MonthlyContribution__c) : '-';
                ocInfo.ral = (oc.RAL__c != null) ? String.valueOf(oc.RAL__c) : '-';
                ocInfo.yearlyGrowth = (oc.ExpectedYearlyGrowth__c != null) ? String.valueOf(oc.ExpectedYearlyGrowth__c) : '-';
                ocInfo.previdentialGap = (oc.PrevidentialGap__c != null) ? String.valueOf(oc.PrevidentialGap__c) : '-';
                ocInfo.retirementYear = (oc.YearOfRetirement__c != null) ? String.valueOf(oc.YearOfRetirement__c) : '-';
                ocInfo.targetProduct = (oc.ProductOfInterest__c != null) ? oc.ProductOfInterest__c : '-';
                ocInfo.numOfChildren = (oc.NumberOfChildren__c != null) ? String.valueOf(oc.NumberOfChildren__c) : '-';
                ocInfo.sector = (oc.Sector__c != null) ? oc.Sector__c : '-';
            }

            ocsInfo.add(ocInfo);
        }
        return ocsInfo;
    }

    /******************************************************************************************
    * @description  Formats a string of assets into a list of strings.
    * @param        assets - String of assets separated by new lines
    * @return       List<String> - List of formatted assets
    *******************************************************************************************/
    private static List<String> formatedAssets(String assets) {
        return new List<String>(assets.split('\n'));
    }

    /******************************************************************************************
    * @description  Formats a string of descriptions into a list of strings.
    * @param        description - String of descriptions separated by new lines
    * @return       List<String> - List of formatted descriptions
    *******************************************************************************************/
    private static List<String> formatedDescription(String description) {
        return new List<String>(description.split('\n'));
    }

    /******************************************************************************************
    * @description  Formats a string of conventions into a list of strings.
    * @param        conventions - String of conventions separated by new lines
    * @return       List<String> - List of formatted conventions
    *******************************************************************************************/
    private static List<String> formatedConventions(String conventions) {
        return new List<String>(conventions.split('\n'));
    }
    
    /******************************************************************************************
    * @description  Formats a string of fractionation details into a list of strings.
    * @param        fractionation - String of fractionation details separated by new lines
    * @return       List<String> - List of formatted fractionation details
    *******************************************************************************************/
    private static List<String> formatedFractionation(String fractionation) {
        List<String> fractionantionList = new List<String>(fractionation.split('\n'));
        List<String> newFractionantionList = new List<String>();
        Map<String, Object> mappaValori = getFieldValuesMappingByObjectField ('Opportunity', 'Frazionamento');
        
        for(String fract : fractionantionList){
            fract = mappaValori.get(fract) != null ? (String) mappaValori.get(fract) : fract;
            newFractionantionList.add(fract);
        }
        System.debug('formatedFractionation - newFractionantionList: ' +fractionantionList);
        return newFractionantionList;
    }

    private static Map<String, Object> getFieldValuesMappingByObjectField (String objectName, String filedName){
        Map<String, Object> mappaValori = new Map<String, Object>();
        if(String.isBlank(objectName) || String.isBlank(filedName)){
            return null;
        }
        List<FieldsValuesMapping__mdt> mappingMDT = [SELECT FieldValue__c, DisplayValue__c FROM FieldsValuesMapping__mdt WHERE ObjectName__c = :objectName AND FieldName__c = :filedName];
        for(FieldsValuesMapping__mdt elem : mappingMDT) {
            mappaValori.put(elem.FieldValue__c, elem.DisplayValue__c);
        }
        System.debug('getFieldValuesMappingByObjectField - mappaValori: ' +mappaValori);
        return mappaValori;
    }

    /******************************************************************************************
    * @description  Combines first name and last name into a full name string.
    * @param        firstName - First name
    * @param        lastName - Last name
    * @return       String - Combined full name or '-' if both names are null
    *******************************************************************************************/
    private static String fullName(String firstName, String lastName) {
        return (firstName != null && lastName != null) ? firstName + ' ' + lastName : (firstName != null && lastName == null) ? firstName : (firstName == null && lastName != null) ? lastName : '-'; 
    }

    /******************************************************************************************
    * @description 
    * @param        ipInput: la mappa contiene url
    * @return       Map<String, Object> - 
    *******************************************************************************************/
        @AuraEnabled(cacheable=false)
    public static Map<String, Object> getPDFDocument(Map<String, Object> ipInput, String ipType) {
        System.debug('ipInput: '+ipInput);
        System.debug('ipType: '+ipType);
        Map<String, Object> response = new Map<String, Object>();
        Savepoint sp = null;
        
        // Validazione del nome procedura
        if (String.isBlank(ipType)) {
            response.put('error', true);
            response.put('errMsg', 'Procedure API Name non può essere nullo o vuoto');
            return response;
        }
        
        try {
            // Invoco la procedura di integrazione
            Map<String, Object> ret = AddressService.invokeIntegrationProcedureLookup(ipType, ipInput, null);
            Object rawReturnList = ret.get('returnList');

            // Parsing ricorsivo fino ad ottenere una Map<String,Object>
            Map<String, Object> returnList = parseToMap(rawReturnList);
            if (returnList == null) {
                response.put('error', true);
                response.put('errMsg', 'Formato di returnList non valido: ' + String.valueOf(rawReturnList));
                return response;
            }

            // Se la mappa non è vuota
            if (!returnList.isEmpty()) {
                if (!returnList.containsKey('base64')) {
                    Integer status = (Integer) returnList.get('status');

                    // Gestione del campo "error"
                    Object rawErrObj = returnList.get('error');
                    Map<String, Object> errObjMap = parseToMap(rawErrObj);
                    String errorMessage;
                    if (errObjMap != null && errObjMap.containsKey('errorMessage')) {
                        errorMessage = String.valueOf(errObjMap.get('errorMessage'));
                    } else if (rawErrObj instanceof String) {
                        errorMessage = (String) rawErrObj;
                    } else {
                        errorMessage = 'Errore sconosciuto';
                    }

                    response.put('error', true);
                    response.put('errMsg', errorMessage);
                    response.put('status', status);
                } else {
                    sp = Database.setSavepoint();
                    String base64 = (String) returnList.get('base64');
                    Blob pdfBlob = EncodingUtil.base64Decode(base64);

                    ContentVersion cv = new ContentVersion(
                        Title        = Date.today().format() + '_pdfPreventivo',
                        PathOnClient = Date.today().format() + '_pdfPreventivo.pdf',
                        VersionData  = pdfBlob
                    );
                    insert cv;

                    ContentDistribution cd = new ContentDistribution(
                        Name                          = cv.Title,
                        ContentVersionId              = cv.Id,
                        PreferencesAllowViewInBrowser = true,
                        PreferencesAllowOriginalDownload = false
                    );
                    insert cd;

                    cd = [
                        SELECT Id, DistributionPublicUrl, ContentVersionId
                        FROM ContentDistribution
                        WHERE Id = :cd.Id
                        LIMIT 1
                    ];
                    response.put('error', false);
                    response.put('contentDistribution', cd);
                }
            } else {
                response.put('error', true);
                response.put('errMsg', 'Nessun file trovato.');
            }
        } catch (Exception e) {
            if (sp != null) Database.rollback(sp);
            response.put('error', true);
            response.put('errMsg', e.getMessage());
            response.put('errStackTrace', e.getStackTraceString());
        }
        return response;
    }

    /**
     * Ricorsivamente deserializza fino ad ottenere una Map<String,Object>
     */
    private static Map<String, Object> parseToMap(Object obj) {
        if (obj instanceof Map<String, Object>) {
            return (Map<String, Object>) obj;
        }
        if (obj instanceof String) {
            String jsonString = ((String) obj).trim();
            try {
                Object des = System.JSON.deserializeUntyped(jsonString);
                return parseToMap(des);
            } catch (Exception ex) {
                return null;
            }
        }
        return null;
    }





    @AuraEnabled(cacheable=false)
    public static Map<String, Object> deletePDFDocument(String distributionId){
        Map<String, Object> response = new Map<String, Object>();
        SavePoint sp = Database.setSavepoint();
        try {
            ContentDistribution dist = [SELECT Id, ContentVersionId FROM ContentDistribution WHERE Id = :distributionId];
            if(dist != null && String.isNotBlank(dist.ContentVersionId)){
                ContentVersion ver = [SELECT Id, ContentDocumentId FROM ContentVersion WHERE Id = :dist.ContentVersionId];
                if(ver != null && String.isNotBlank(ver.ContentDocumentId)){
                    ContentDocument doc = [SELECT Id FROM ContentDocument WHERE Id = :ver.ContentDocumentId];
                    delete doc;
                }
            }
            response.put('error', false);
        } catch(Exception e){
            Database.rollback(sp);
            response.put('error', true);
            response.put('errMsg', e.getMessage());
            response.put('errStackTrace', e.getStackTraceString());
        }
        return response;
    }
}