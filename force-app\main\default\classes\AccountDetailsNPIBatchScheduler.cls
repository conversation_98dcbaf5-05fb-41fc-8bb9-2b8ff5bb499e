global class AccountDetailsNPIBatchScheduler implements Schedulable {


    /***
      //Esempio di schedulazione da script apex con  filtri che
      //rappresentano l’intervallo temporale dal primo luglio 2025 (inizio giornata) al 31 luglio 2025 (fine giornata)
      //ovvero -> dal 1 luglio 2025 alle 00:00:00 (mezzanotte) al 31 luglio 2025 alle 23:59:59 (un secondo prima della mezzanotte)

        Set<Id> idsSet = new Set<Id>{'a01XXXXXXXXXXXX', 'a01YYYYYYYYYYYY'}; // Sostituisci con Id reali
        Id idValueFrom = 'a01XXXXXXXXXXXX';// Sostituisci con Id reale
        Id idValueTo = 'a01YYYYYYYYYYYY';// Sostituisci con Id reale
        DateTime dateFrom = DateTime.newInstance(2025, 7, 1, 0, 0, 0);
        DateTime dateTo = DateTime.newInstance(2025, 7, 31, 23, 59, 59);

        String cron = '0 0 2 * * ?'; // Ogni giorno alle 2:00

        System.schedule(
            'AccountDetailsNPIBatch Parametrico',
            cron,
            new AccountDetailsNPIBatchScheduler(idsSet, idValueFrom, idValueTo, dateFrom, dateTo)
        );
     * 
     * 
     */


    private Set<Id> idsSet;
    private Id idValueFrom;
    private Id idValueTo;
    private DateTime dateFrom;
    private DateTime dateTo;

    // Costruttore parametrico
    global AccountDetailsNPIBatchScheduler(Set<Id> idsSet, Id idValueFrom, Id idValueTo, DateTime dateFrom, DateTime dateTo) {
        this.idsSet = idsSet;
        this.idValueFrom = idValueFrom;
        this.idValueTo = idValueTo;
        this.dateFrom = dateFrom;
        this.dateTo = dateTo;
    }

    // Costruttore vuoto per compatibilità (opzionale)
    global AccountDetailsNPIBatchScheduler() {}

    global void execute(SchedulableContext sc) {
        AccountDetailsNPIBatch batch = new AccountDetailsNPIBatch(idsSet, idValueFrom, idValueTo, dateFrom, dateTo);
        Database.executeBatch(batch, 200);
    }
}