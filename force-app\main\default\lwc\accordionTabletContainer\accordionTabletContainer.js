import { LightningElement, api, track } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';

import getQuotesInfo from '@salesforce/apex/AccordionController.getQuotesInfo';

import areas_of_need_images from '@salesforce/resourceUrl/areas_of_need';

const PET_IMG = areas_of_need_images + '/on/pet.png';
const HOUSE_IMG = areas_of_need_images + '/on/casa.png';
const FAMILY_IMG = areas_of_need_images + '/on/famiglia.png';
const INJURIES_IMG = areas_of_need_images + '/on/infortuni.png';
const MOTORCYCLE_IMG = areas_of_need_images + '/on/mobilita.png';
const HEALTH_IMG = areas_of_need_images + '/on/salute.png';
const CAR_IMG = areas_of_need_images + '/on/veicoli.png';
const TRAVEL_IMG = areas_of_need_images + '/on/viaggi.png';
const VITA_IMG = areas_of_need_images + '/on/vita.png';
const MIN_SCREEN_SIZE_FOR_PC = 1200;

export default class AccordionTabletContainer extends LightningElement{
    @api recordId;
        
    @track quotes = [];
    
    @track firstQuote = [];
    
    @track showAll;
    @track showAllText;
    @track hasQuote = false;
    @track hasMultipleQuotes = false;

    // Method to handle click event
    handleClick() {
        this.showAll = !this.showAll;
        this.showAllText = (this.showAll === false) ? 'Mostra tutti' : 'Nascondi';
    }

    @track isTablet = window.innerWidth <= MIN_SCREEN_SIZE_FOR_PC;

    // Method called when the component is connected to the DOM
    connectedCallback() {
        this.fetchQuotes();
        
        window.addEventListener('resize', () => {
            this.isTablet = window.innerWidth <= MIN_SCREEN_SIZE_FOR_PC;
        });
    }

    // Method to fetch quotes
    fetchQuotes() {
        getQuotesInfo({ recordId: this.recordId })
        .then(data => {
            for (let i = 0; i < data.length; i++) {
                let _opportunityCoverages = [];
                for (let j = 0; j < data[i].opportunityCoverages.length; j++) {
                    
                    let opportunityCoverage = {
                        isFirst: j === 0 ? true : false,
                        areaOfNeedImage: this.convertAreaOfNeedToImage(data[i].opportunityCoverages[j].areaOfNeed),
                        amount: data[i].opportunityCoverages[j].amount,
                        assetItems: data[i].opportunityCoverages[j].assets,
                        descriptionItems: data[i].opportunityCoverages[j].description,
                        conventions: data[i].opportunityCoverages[j].conventions,
                        fullName: data[i].opportunityCoverages[j].fullName,
                        splitting: data[i].opportunityCoverages[j].splitting,
                        stage: data[i].opportunityCoverages[j].stage
                    }

                    _opportunityCoverages.push(opportunityCoverage);
                }
                    
                let quote = {
                    isOpportunityClosed: data[i].opportunityStage === 'Chiuso' ? true : false,
                    name: data[i].name,
                    recordId: data[i].recordId,
                    isFirst: i === 0 ? true : false,
                    areasOfNeedImages: this.convertAreasOfNeedToImages(data[i].areasOfNeed),
                    status: data[i].status,
                    totalAmount: data[i].totalAmount,
                    creationDate: data[i].creationDate,
                    expirationDate: data[i].expirationDate,
                    unicaLink: data[i].unicaLink,
                    opportunityCoverages: _opportunityCoverages
                }

                this.hasQuote = true;
                this.quotes.push(quote);

                if(i === 0)
                    this.firstQuote.push(quote);
            }
            this.showAll = false;
            this.showAllText = 'Mostra tutti';

            this.hasMultipleQuotes = this.quotes.length > 1;

        }).catch(error => {
            console.log(error.body.message);
            this.showToast('An error occured, please contact System Admin', error.body.message, 'error'); 
        });
    }

    // Method to convert areas of need to images
    convertAreasOfNeedToImages(areasOfNeed) {
        let areasOfNeedImages = [];
        for (let i = 0; i < areasOfNeed.length; i++) {
            areasOfNeedImages.push(this.convertAreaOfNeedToImage(areasOfNeed[i]));
        }
        return areasOfNeedImages;
    }

    // Method to convert areas of need to images
    convertAreaOfNeedToImage(areaOfNeed) {
        switch(areaOfNeed) {
            case 'Cane e Gatto':
                return PET_IMG;
            case 'Casa':
                return HOUSE_IMG;
            case 'Famiglia':
                return FAMILY_IMG;
            case 'Infortuni':
                return INJURIES_IMG;
            case 'Mobilita':
                return MOTORCYCLE_IMG;
            case 'Salute':
                return HEALTH_IMG;
            case 'Veicoli':
                return CAR_IMG;
            case 'Viaggio':
                return TRAVEL_IMG;
                case 'Vita':         return VITA_IMG;
    case 'Previdenza integrativa': return VITA_IMG;
    case 'Persona':return VITA_IMG;
    case 'Casa e Famiglia': return VITA_IMG;
            default:
                console.log('There was an error retrieving the image for the Area Of Need');
                return null;
            }
    }
    
    // Method to show a toast notification
    showToast(title, message, variant){ 
        const event = new ShowToastEvent({ 
            title: title, 
            message: message,         
            variant: variant         
            }); 
        this.dispatchEvent(event); 
    } 
}