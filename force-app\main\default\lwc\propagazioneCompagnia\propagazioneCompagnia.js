import { LightningElement, api, track } from 'lwc';
import userHasMultipleMandati from '@salesforce/apex/AnagraficaManagement.userHasMultipleMandati';

export default class PropagazioneCompagnia extends LightningElement {

    @api
    setInit;
    @api
    selectedValues = [];
    @api
    optionsCompagnie = [
        /*
        { label: 'UnipSalute', value: 'unisalute' },
        { label: 'UnipolSai', value: 'unipolsai' },
        { label: 'UnipolTech', value: 'unipoltech' }
        */
    ];
    @api
    getCompagnieSelezionate() {
        return this.selectedCompany;
    }

    @track
    compagnie = "";
    @track
    propaga = false;
    @track
    initDone = false;
    @track
    selectedCompany = [];
    @track
    optionsCompagnieFilter = [];
    @track
    propagazioneCompagniaDisabled = true;

    connectedCallback() {
        //AC: come spiegato in testa, questo intervallo è necessario per verificare che gli attributi vengano passati dalla flex card.
        //    per evitare che il ciclo venga eseguito all'infinito in caso di mancata assegnazione di setInit, imposto un contatore.
        //    il contatore arriva fino a 20 (per un totale di 2 secondi), se non viene inizializzato, interrompe l'intervallo e mosta un messaggio di errore.
        let cnt = 0;
        const ci = setInterval(() => {
            if (this.setInit) {
                this.init();
                clearInterval(ci);
            } else {
                cnt++;
                if (cnt > 20) {
                    clearInterval(ci);
                    this.error("errore inzializzazione");
                }
            }
        }, 100);
    }

    disconnectedCallback() {
    }

    renderedCallback() {
    }

    error(err) {
        console.log(err);
        //this.errorInit = true;
    }

    init() {    
        this.optionsCompagnieFilter = [...this.optionsCompagnie];
        //AC: valutare se, a questo punto, sia il caso di mettere un servizio che restituisce
        //    le compagnie del mandato
        userHasMultipleMandati()
            .then(result => {
                if (!result.error) {
                    this.propagazioneCompagniaDisabled = !result.hasMultipleMandati;
                }
            })
            .catch(error => {
                console.error('Errore durante il recupero dei mandati:', error);
            }).finally (() => {
                this.initDone = true;
            });
    }


    handleCheckboxChange(e) {
        //this.propaga = e.target.checked;
    }

    handleSelectChange(event) {
        /*
        const selected = Array.from(event.target.selectedOptions).map(opt => opt.value);
        this.selectedValues = selected;
        console.log('Selezionate:', this.selectedValues);
        */
       // let selectedCompany = this.addOptionCompany(event.detail.value);
       // this.filteroptionsCompagnie(selectedCompany);
    }

    addOptionCompany(value) {
        let selectedCompany = this.optionsCompagnieFilter.find(obj => obj.value === value);
        this.selectedCompany.push(selectedCompany);
        return selectedCompany;
    }

    clear(event) {
        //let selectedPill = {value: event.detail.value, label: event.detail.name};
        let selectedPill = this.optionsCompagnie.find(obj => obj.value === event.detail.name);
        this.optionsCompagnieFilter = this.optionsCompagnieFilter.concat(selectedPill);
        this.selectedCompany = this.selectedCompany.filter(companyOption => {
            return companyOption.value !== selectedPill.value;
        });
    }

    filteroptionsCompagnie(selectedCompany){
        this.optionsCompagnieFilter = this.optionsCompagnieFilter.filter(companyOption => {
            return companyOption.value !== selectedCompany.value;
        });
    }
}