@isTest
public class AccountDetailsNPIBatchSchedulerTest {
    @isTest
    static void testSchedulerWithParams() {
        // Dati di test
        AccountDetailsNPI__c npi1 = new AccountDetailsNPI__c(Name = 'Test NPI 1');
        AccountDetailsNPI__c npi2 = new AccountDetailsNPI__c(Name = 'Test NPI 2');
        insert new List<AccountDetailsNPI__c>{npi1, npi2};

        Set<Id> idsSet = new Set<Id>{npi1.Id, npi2.Id};
        Id idValueFrom = npi1.Id;
        Id idValueTo = npi2.Id;
        DateTime dateFrom = DateTime.newInstance(2025, 7, 1, 0, 0, 0);
        DateTime dateTo = DateTime.newInstance(2025, 7, 31, 23, 59, 59);

        Test.startTest();
        AccountDetailsNPIBatchScheduler scheduler = new AccountDetailsNPIBatchScheduler(idsSet, idValueFrom, idValueTo, dateFrom, dateTo);
        scheduler.execute(null); // SchedulableContext non necessario per test
        Test.stopTest();

    }

    @isTest
    static void testSchedulerDefaultConstructor() {
        Test.startTest();
        AccountDetailsNPIBatchScheduler scheduler = new AccountDetailsNPIBatchScheduler();
        scheduler.execute(null);
        Test.stopTest();
    }
}