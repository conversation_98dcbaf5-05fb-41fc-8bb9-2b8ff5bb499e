<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <fieldLevelSecurityEnabled>true</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>DRTRenewalsDashboardDatatable</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsDashboardDatatableCustom5828</globalKey>
        <inputFieldName>dataList:empty</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsDashboardDatatable</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>empty</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:titoliIncassati var:titoliNonIncassati var:totaleTitoli LIST</formulaConverted>
        <formulaExpression>LIST(titoliIncassati,titoliNonIncassati,totaleTitoli)</formulaExpression>
        <formulaResultPath>dataList</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>DRTRenewalsDashboardDatatableCustom138</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsDashboardDatatable</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsDashboardDatatableCustom1410</globalKey>
        <inputFieldName>dataList:numero</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsDashboardDatatable</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Double</outputFieldFormat>
        <outputFieldName>numero</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsDashboardDatatableCustom3352</globalKey>
        <inputFieldName>dataList:premio</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsDashboardDatatable</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Currency</outputFieldFormat>
        <outputFieldName>premio</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DRTRenewalsDashboardDatatableCustom3387</globalKey>
        <inputFieldName>dataList:label</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DRTRenewalsDashboardDatatable</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;totaleTitoli&quot; : {
    &quot;label&quot; : &quot;Totale&quot;,
    &quot;premio&quot; : &quot;€1.542.300,50&quot;,
    &quot;numero&quot; : 1050
  },
  &quot;titoliIncassati&quot; : {
    &quot;empty&quot; : false,
    &quot;label&quot; : &quot;Incassati&quot;,
    &quot;numero&quot; : 800,
    &quot;premio&quot; : &quot;€1.200.000,00&quot;
  },
  &quot;titoliNonIncassati&quot; : {
    &quot;label&quot; : &quot;Da incassare&quot;,
    &quot;premio&quot; : &quot;€342.300,50&quot;,
    &quot;numero&quot; : 250
  }
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>DRTRenewalsDashboardDatatable_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
