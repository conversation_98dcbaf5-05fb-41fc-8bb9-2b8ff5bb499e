<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>pushNotificationCreation</name>
        <label>pushNotificationCreation</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <elementReference>notTypeId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIds</name>
            <value>
                <elementReference>recipientIdCollection</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <elementReference>$Label.PushNotificationTitleCreation</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <elementReference>caseIdPopulateFormula1</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetId</name>
            <value>
                <elementReference>varTargetRecordId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>pushNotificationReassign</name>
        <label>pushNotificationReassign</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <elementReference>notTypeId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIds</name>
            <value>
                <elementReference>recipientIdCollection</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <elementReference>$Label.PushNotificationTitleReaasign</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <elementReference>caseIdPopulateFormula2</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetId</name>
            <value>
                <elementReference>varTargetRecordId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>64.0</apiVersion>
    <assignments>
        <name>assignGroup</name>
        <label>assignGroup</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>recipientIdCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>assignToGroup</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>creationOrReassign</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assignPerson</name>
        <label>assignPerson</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>recipientIdCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>assignTo</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>creationOrReassign</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assignToAndAssignToGroup</name>
        <label>assignToAndAssignToGroup</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>recipientIdCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>assignToGroup</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>recipientIdCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>assignTo</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>creationOrReassign</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>creationOrReassign</name>
        <label>creationOrReassign</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>creation</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>notType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Creation</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>pushNotificationCreation</targetReference>
            </connector>
            <label>creation</label>
        </rules>
        <rules>
            <name>reassign</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>notType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Reassignment</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>pushNotificationReassign</targetReference>
            </connector>
            <label>reassign</label>
        </rules>
    </decisions>
    <decisions>
        <name>Decision_2</name>
        <label>Decision 2</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>assignToAndAssignToGroup</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>assignToPerson</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>assignTo</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>assignToGroup</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>assignPerson</targetReference>
            </connector>
            <label>assignToPerson</label>
        </rules>
        <rules>
            <name>ifAssignToGroup</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>assignToGroup</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>assignTo</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>assignGroup</targetReference>
            </connector>
            <label>ifAssignToGroup</label>
        </rules>
    </decisions>
    <description>To display/send push notification</description>
    <environments>Default</environments>
    <formulas>
        <name>caseIdPopulateFormula1</name>
        <dataType>String</dataType>
        <expression>SUBSTITUTE({!$Label.PushNotificationBodyCreation}, &quot;[task ID]&quot;, {!caseNumber})</expression>
    </formulas>
    <formulas>
        <name>caseIdPopulateFormula2</name>
        <dataType>String</dataType>
        <expression>SUBSTITUTE({!$Label.PushNotificationBodyReassignment}, &quot;[task ID]&quot;, {!caseNumber})</expression>
    </formulas>
    <interviewLabel>notificationSystemOrchestrator {!$Flow.CurrentDateTime}</interviewLabel>
    <label>notificationSystemOrchestrator</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Decision_2</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>assignTo</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>assignToGroup</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>caseNumber</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>currentItem_metadataFilterCreation</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>globalNotificationSchema__mdt</objectType>
    </variables>
    <variables>
        <name>notType</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>notTypeId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>recipientIdCollection</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>varTargetRecordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
</Flow>
