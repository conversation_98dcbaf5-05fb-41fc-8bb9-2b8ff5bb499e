<template>
    <template if:true={showModal}>
        <!-- Aggiorna il loader: utilizza attribute data-ref nel template -->
        <c-loader data-ref="loader"></c-loader>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open custom-modal">
            <div class="slds-modal__container custom-modal__container">
                <header class="slds-modal__header">
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Chiudi anteprima" onclick={handleClose}>
                        <lightning-icon icon-name="utility:close" alternative-text="Chiudi anteprima" size="small"></lightning-icon>
                        <span class="slds-assistive-text">Chiudi anteprima</span>
                    </button>
                    <h2 class="slds-text-heading_medium">{title}</h2>
                </header>
                <div class="slds-modal__content custom-modal__content">
                    <template if:true={isPdfExisting}>
                        <template if:true={errorGetUrl}>
                            <h3 class="slds-text-color_error">{errorMsgUrl}</h3>
                        </template>
                        <template if:false={errorGetUrl}>
                            <iframe src={urlPDF} width={frameWidth} height={frameHeight}></iframe>
                        </template>
                    </template>
                    <template if:false={isPdfExisting}>
                        <h3>Indirizzo PDF non disponibile</h3>
                    </template>
                </div>
                <footer class="slds-modal__footer">
                    <lightning-button variant="neutral" label="Chiudi" onclick={handleClose}></lightning-button>
                    <lightning-button variant="brand" label="Scarica" onclick={downloadPdf} disabled={isDownloadDisabled}></lightning-button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
</template>