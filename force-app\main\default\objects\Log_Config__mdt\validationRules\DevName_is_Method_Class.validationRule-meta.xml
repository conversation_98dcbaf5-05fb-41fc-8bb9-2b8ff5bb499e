<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>DevName_is_Method_Class</fullName>
    <active>false</active>
    <description>Enforces that the api name of a cmdt instance is the concatenation of &quot;source_className_methodName&quot;</description>
    <errorConditionFormula>If(
 ISPICKVAL(Source__c, &apos;Apex&apos;), 
  DeveloperName !=  &apos;Apex_&apos; + ClassName__c + &apos;_&apos; +  MethodName__c,
 IF(
  ISPICKVAL(Source__c, &apos;OmniStudio&apos;), 
   DeveloperName !=  &apos;OS_&apos; + ClassName__c + &apos;_&apos; +  MethodName__c,
  false
 )
)</errorConditionFormula>
    <errorDisplayField>DeveloperName</errorDisplayField>
    <errorMessage>the record developer must be a concatenation of (1) source (2) className and (3) methodName joined by underscore (_).
example : OS_myClassName_myMethodName</errorMessage>
</ValidationRule>
