<template>
            <div class="slds-grid slds-wrap slds-is-relative ">
            <div style="position: fixed;z-index: 999999;top: 0;right: 0;">
  <omnistudio-toast class="flexcard-toast-element" theme="slds" title="" message="" styletype=""> </omnistudio-toast>
</div>
            <div if:true={showLoader} class="slds-is-absolute vloc-loader_override" style="height: 100%; width: 100%; min-height:50px; background: transparent; z-index: 99;">
  <div>
   <omnistudio-spinner
      variant="brand"
      alternative-text="Loading content..."
      size="medium"
      theme="slds"
      ></omnistudio-spinner>
  </div>
</div>
            <template if:false={hasPermission}>
              You don't have required permissions to view this card.
            </template>
            <template if:true={hasPermission}>
              <template if:true={hasRecords}>
                      <omnistudio-flex-card-state   data-statue="true"  data-index="0" data-rindex={rindex} class="cf-vlocity-state-0 cf-vlocity-state slds-size_12-of-12 "  tracking-obj={_childCardTrackingObj} >
      <div if:true={cardLoaded} class="slds-grid slds-wrap slds-card slds-p-around_x-small slds-m-around_none " style="      
         ">
          <div data-style-id="state0element0" class="slds-col   slds-size_12-of-12 " data-rindex={rindex} style="" ><omnistudio-flex-datatable data-style-id="state0element0_child"   card={card}  record={record}  issearchavailable="false"  issortavailable="true"  cell-level-edit="true"  pagelimit="3"  group-order="asc"  search-datatable=""  columns='\[{"fieldName":"compagnia","label":"Società","searchable":false,"sortable":true,"type":"text"},{"fieldName":"tipo","label":"Tipo","searchable":false,"sortable":true,"type":"text"},{"fieldName":"ambito","label":"Ambito","searchable":false,"sortable":true,"type":"text"},{"fieldName":"polizza","label":"Numero Polizza/Posizione","searchable":false,"sortable":true,"type":"text"},{"fieldName":"dataScadenzaTitolo","label":"Scadenza","searchable":false,"sortable":true,"type":"text"},{"fieldName":"totale","label":"Premio","searchable":false,"sortable":true,"type":"text"}]'  records={records}  theme="slds"  ></omnistudio-flex-datatable>
      </div><div data-style-id="state0element1" class="slds-col   slds-text-link_reset  slds-p-around_x-small slds-size_12-of-12 " data-rindex={rindex} style="" >
    <omnistudio-block data-style-id="state0element1_child"  action='\{"label":"Action","iconName":"standard-default","eventType":"onclick","actionList":[{"key":"*************-kw779pgo6","label":"Action","draggable":false,"isOpen":true,"card":"{card}","stateAction":{"id":"flex-action-*************","type":"Custom","displayName":"Action","vlocityIcon":"standard-default","targetType":"Web Page","openUrlIn":"New Tab/Window","Web Page":{"targetName":"/lightning/n/Titoli_in_scadenza?c__AccountId={recordId}"}},"actionIndex":0}],"showSpinner":"false"}'  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  extraclass=" slds-text-link_reset"  theme="slds"  onclick={executeAction} data-element-label="block1" data-action-key="state0element1" onkeydown={executeActionWithKeyboard} tabindex="0"><div class="slds-grid slds-wrap">
          <div data-style-id="state0element1block_element0" class="slds-col   slds-size_12-of-12  " data-rindex={rindex} style="      
         color:#0070EF;
text-align: center;
font-size: 13px;
font-style: normal;
font-weight: 600;
line-height: normal;
text-decoration-line: underline;
text-decoration-style: solid;
text-decoration-skip-ink: none;
text-decoration-thickness: auto;
text-underline-offset: auto;
text-underline-position: from-font;" ><omnistudio-output-field data-style-id="state0element1block_element0_child"   card={card}  record={record}  merge-field="%3Cdiv%3EVisualizza%20Tutto%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div>
        </div></omnistudio-block>
    </div>
        </div>
      </omnistudio-flex-card-state>
                </template>
                <template if:false={hasRecords}>
                      <omnistudio-flex-card-state   data-statue="false"  data-index="0" data-rindex={rindex} class="cf-vlocity-state-0 cf-vlocity-state slds-size_12-of-12 "  tracking-obj={_childCardTrackingObj} >
      <div if:true={cardLoaded} class="slds-grid slds-wrap slds-card slds-p-around_x-small slds-m-around_none " style="      
         ">
          <div data-style-id="state0element0" class="slds-col   slds-size_12-of-12 " data-rindex={rindex} style="" ><omnistudio-flex-datatable data-style-id="state0element0_child"   card={card}  record={record}  issearchavailable="false"  issortavailable="true"  cell-level-edit="true"  pagelimit="3"  group-order="asc"  search-datatable=""  columns='\[{"fieldName":"compagnia","label":"Società","searchable":false,"sortable":true,"type":"text"},{"fieldName":"tipo","label":"Tipo","searchable":false,"sortable":true,"type":"text"},{"fieldName":"ambito","label":"Ambito","searchable":false,"sortable":true,"type":"text"},{"fieldName":"polizza","label":"Numero Polizza/Posizione","searchable":false,"sortable":true,"type":"text"},{"fieldName":"dataScadenzaTitolo","label":"Scadenza","searchable":false,"sortable":true,"type":"text"},{"fieldName":"totale","label":"Premio","searchable":false,"sortable":true,"type":"text"}]'  records={records}  theme="slds"  ></omnistudio-flex-datatable>
      </div><div data-style-id="state0element1" class="slds-col   slds-text-link_reset  slds-p-around_x-small slds-size_12-of-12 " data-rindex={rindex} style="" >
    <omnistudio-block data-style-id="state0element1_child"  action='\{"label":"Action","iconName":"standard-default","eventType":"onclick","actionList":[{"key":"*************-kw779pgo6","label":"Action","draggable":false,"isOpen":true,"card":"{card}","stateAction":{"id":"flex-action-*************","type":"Custom","displayName":"Action","vlocityIcon":"standard-default","targetType":"Web Page","openUrlIn":"New Tab/Window","Web Page":{"targetName":"/lightning/n/Titoli_in_scadenza?c__AccountId={recordId}"}},"actionIndex":0}],"showSpinner":"false"}'  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  extraclass=" slds-text-link_reset"  theme="slds"  onclick={executeAction} data-element-label="block1" data-action-key="state0element1" onkeydown={executeActionWithKeyboard} tabindex="0"><div class="slds-grid slds-wrap">
          <div data-style-id="state0element1block_element0" class="slds-col   slds-size_12-of-12  " data-rindex={rindex} style="      
         color:#0070EF;
text-align: center;
font-size: 13px;
font-style: normal;
font-weight: 600;
line-height: normal;
text-decoration-line: underline;
text-decoration-style: solid;
text-decoration-skip-ink: none;
text-decoration-thickness: auto;
text-underline-offset: auto;
text-underline-position: from-font;" ><omnistudio-output-field data-style-id="state0element1block_element0_child"   card={card}  record={record}  merge-field="%3Cdiv%3EVisualizza%20Tutto%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div>
        </div></omnistudio-block>
    </div>
        </div>
      </omnistudio-flex-card-state>
                </template>
            </template>
            <template if:true={hasError}>
            {error}
            </template>
            
      <omnistudio-action action-wrapperclass="slds-hide" re-render-flyout class="action-trigger slds-col" onupdateos={updateOmniscript} onselectcards={updateSelectedCards} onsetvalue={updateAction} onfireactionevent={actionEvtHandler}>
      
      </omnistudio-action>
  
            <omnistudio-action class="execute-action" re-render-flyout action-wrapperclass="slds-hide" onupdateos={updateOmniscript} onselectcards={updateSelectedCards} onsetvalue={updateAction} onfireactionevent={actionEvtHandler}>
            
            </omnistudio-action>
            </div>
          </template>