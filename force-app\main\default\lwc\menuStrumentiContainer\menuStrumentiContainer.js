import { LightningElement, track } from 'lwc';
import { getFocusedTabInfo, setTabLabel, setTabIcon } from 'lightning/platformWorkspaceApi';
import { ShowToastEvent } from "lightning/platformShowToastEvent";

import getTabs from '@salesforce/apex/MenuStrumentiController.getTabs';
import getSections from '@salesforce/apex/MenuStrumentiController.getSections';
import getTreeByContext from '@salesforce/apex/MenuStrumentiController.getTreeByContext';
import getUserFavorite from '@salesforce/apex/MenuStrumentiController.getUserFavorite';
import setUserFavorite from '@salesforce/apex/MenuStrumentiController.setUserFavorite';
import removeUserFavorite from '@salesforce/apex/MenuStrumentiController.removeUserFavorite';
import getParamsForFei from '@salesforce/apex/MenuStrumentiController.getParamsForFei';
import getUserCf from '@salesforce/apex/MenuStrumentiController.getUserCf';

import MENU_STRUMENTI_UNIPOL_LOGO from "@salesforce/resourceUrl/menuStrumentiUnipol";
import MENU_STRUMENTI_UNISALUTE_LOGO from "@salesforce/resourceUrl/menuStrumentiUnisalute";

//import { registerRefreshContainer, REFRESH_COMPLETE, REFRESH_COMPLETE_WITH_ERRORS, REFRESH_ERROR } from 'lightning/refresh';

// Implementare visibilità tramite mandato dei tab

export default class MenuStrumentiContainer extends LightningElement {
    
    @track _tabs = [];
    @track _favorite = [];

    unipolLogo = MENU_STRUMENTI_UNIPOL_LOGO;
    unisaluteLogo = MENU_STRUMENTI_UNISALUTE_LOGO;

    currentTab;
    notEnabled = false;

    isLoading = 0;

    paramsFEI = {}
    isLWCModalOpened;

    showLoader() {
        this.isLoading = true;
    }

    hideLoader() {
        this.isLoading = false;
    }

    connectedCallback() {
        console.log('MenuStrumentiContainer:: connected callback');

        this.init();
        //this.refreshContainerID = registerRefreshContainer(this, this.refreshContainer);
    }

    disconnectedCallback() {
        console.log("Component disconnected.");
    }

    init() {
        this.showLoader();
        this.setTab();

        this._tabs = [];
        this._favorite = [];

        getUserFavorite().then(favorite => {
            this._favorite = favorite;
            console.log('MenuStrumentiContainer::getUserFavorite:: ' + JSON.stringify(this._favorite));

        }).catch(error => {
            console.log(JSON.stringify(error))
        }).finally(() => {
            console.log('MenuStrumentiContainer::getUserFavorite call finished')
        

            getTabs().then(result  => {
                console.log('MenuStrumentiContainer::getTabs:: ' + JSON.stringify(result));
                if(!result || result.length == 0) {
                    this.notEnabled = true;
                    return;
                }

                result.forEach((element, index) => {
                    const isFirst = (index === 0);
                    if(isFirst) {
                        this.currentTab = element.name;
                    }
                    let _temp = {
                        "name": element.name,
                        "label": element.label,
                        "context": element.context,
                        "userContext": element.userContext ? element.userContext : null,
                        "isUnipol": (element.name.includes('UNIPOL')),
                        "isUnisalute": (element.name.includes('UNISALUTE')),
                        "isUnirental": (element.name.includes('RENTAL')),
                        "isUnitech": (element.name.includes('TECH')),
                        "show": isFirst,
                        "tabClass": isFirst ? 'slds-tabs_default__item slds-is-active' : 'slds-tabs_default__item',
                        "tabSection": isFirst ? 'slds-tabs_default__content slds-show' : 'slds-tabs_default__content slds-hide',
                        "id": "tab-default-" + element.name + "__item",
                        "ariaControls": "tab-default-" + element.name,
                        "sections": []
                    }
                    this._tabs.push(_temp);
                });

                this._tabs = [...this._tabs];
                console.log('MenuStrumentiContainer::getTabs::Tabs:: ' + JSON.stringify(this._tabs));

                this._tabs.forEach(tabs => {
                    getSections({
                        context: tabs.context
                    }).then(result => {
                        console.log('MenuStrumentiContainer::getSections:: ' + JSON.stringify(result));
                        result.forEach(element => {
                            let _temp = {
                                "sectionLabel" : element.label,
                                "sectionName" : element.name,
                                "sectionContext" : element.context,
                                "sectionShow": false,
                                "sectionMenus" : []
                            }
                            
                            const target = this._tabs.find(tab => tab.context === element.context);

                            console.log('MenuStrumentiContainer::getSections:: ' + JSON.stringify(target));
                            
                            if (target) {
                                // se necessario inizializza la lista
                                if (!Array.isArray(target.sections)) {
                                    target.sections = [];
                                }

                                // Aggiunge i nodi senza creare un array annidato
                                target.sections.push(_temp);
                            }
                        });

                        console.log('MenuStrumentiContainer::getSections::Tabs:: ' + JSON.stringify(this._tabs));

                        getTreeByContext({
                            context: tabs.context,
                            userContext: tabs.userContext
                        }).then(treeContext => {

                            this.showLoader();
                            
                            console.log('MenuStrumentiContainer::getTreeByContext:: ' + JSON.stringify(treeContext));

                            treeContext.forEach(wrapper => {
                                // wrapper.section = “nome sezione” proveniente dall’Apex
                                // wrapper.items = array di nodi radice

                                console.log('MenuStrumentiContainer::getTreeByContext::wrapper:: ' + JSON.stringify(wrapper));

                                const targetSection = this._tabs.find(tab => tab.context === tabs.context).sections;
                                const target = targetSection.find(section => section.sectionName === wrapper.section);

                                console.log('MenuStrumentiContainer::getTreeByContext::targetSection:: ' + JSON.stringify(targetSection));
                                console.log('MenuStrumentiContainer::getTreeByContext::target:: ' + JSON.stringify(target));
                                
                                if (target) {
                                    // se necessario inizializza la lista
                                    if (!Array.isArray(target.sectionMenus)) {
                                        target.sectionMenus = [];
                                    }

                                    // Aggiunge i nodi senza creare un array annidato
                                    target.sectionMenus = [
                                        ...target.sectionMenus,
                                        ...wrapper.items
                                    ];

                                    if(target.sectionMenus.length > 0) {
                                        target.sectionShow = true;
                                    }
                                }

                                console.log('MenuStrumentiContainer::getTreeByContext::target:: ' + JSON.stringify(target));
                            });
                            
                            console.log('MenuStrumentiContainer::getTreeByContext::Tabs:: ' + JSON.stringify(this._tabs));

                            if(this._favorite) {
                                const target = this._tabs.find(tab => tab.context === tabs.context);

                                console.log('MenuStrumentiContainer::getTreeByContext::Tabs:: ' + JSON.stringify(target.sections));

                                target.sections = target.sections.map(sec => ({
                                    ...sec,
                                    sectionMenus: this.markFavorites(sec.sectionMenus)
                                }));
                            }

                            this._tabs = [...this._tabs];
                            
                            this.hideLoader();

                        }).catch(error => {
                            console.log(JSON.stringify(error))
                        }).finally(() => console.log('MenuStrumentiContainer::getTreeByContext call finished'));

                    }).catch(error => {
                        console.log(JSON.stringify(error))
                    }).finally(() => console.log('MenuStrumentiContainer::getSections call finished'));
                });
            }).catch(error => {
                console.log(JSON.stringify(error))
            }).finally(() => {
                console.log('MenuStrumentiContainer::getTabs call finished')
            });
        });
    }

    async setTab() {
        const { tabId } = await getFocusedTabInfo();
        await setTabLabel(tabId, 'Strumenti', 'custom:custom19');
        // opzionale:
        await setTabIcon(tabId,'custom:custom19');
    }

    /*refreshContainer(refreshPromise) {
        console.log("Refreshing component...");
        // Esegui l'aggiornamento dei dati
        this.init(); // Metodo personalizzato per aggiornare i dati
        
        // Gestisci lo stato del refresh
        return refreshPromise.then((status) => {
            if (status === REFRESH_COMPLETE) {
                console.log("Refresh completed successfully!");
            
            } else if (status === REFRESH_COMPLETE_WITH_ERRORS) {
                console.warn("Refresh completed with some issues.");
            
            } else if (status === REFRESH_ERROR) {
                console.error("Refresh failed with major errors.");
            
            }
        });
        
    }*/

    handleTabClick(event) {
        event.preventDefault();

        this.currentTab = event.currentTarget.dataset.id;

        const currentItem = this.template.querySelector(
            'li.slds-tabs_default__item.slds-is-active'
        );
        if (currentItem) {
            currentItem.classList.remove('slds-is-active');

            const currentPanelId = currentItem.querySelector('a').getAttribute('aria-controls');
            const currentPanel = this.template.querySelector(
                `#${currentPanelId}`
            );
            
            if (currentPanel) {
                currentPanel.classList.replace('slds-show', 'slds-hide');
            }
        }

        // Aggiunge lo stato attivo al tab cliccato
        const clickedItem = event.currentTarget.parentElement; // <li>
        clickedItem.classList.add('slds-is-active');

        const panelId = event.currentTarget.getAttribute('aria-controls');
        const panel = this.template.querySelector(`#${panelId}`);
        if (panel) {
            panel.classList.replace('slds-hide', 'slds-show');
        }
    }

    handleBranchToggle(event) {
        const sectionName = event.detail.sectionName;
        const tabs = event.detail.tab;

        // per ogni sezione diversa da quella cliccata,
        // azzero tutti gli expanded dei suoi menu

        const target = this._tabs.find(tab => tab.name === tabs);

        console.log('MenuStrumentiContainer::handleBranchToggle:: ' + JSON.stringify(target.sections));

        target.sections = target.sections.map(sec => {
            if (sec.sectionName !== sectionName) {
            const collapsed = sec.sectionMenus.map(item => ({
                ...item,
                expanded: false
            }));
            return { ...sec, sectionMenus: collapsed };
            }
            return sec;
        });

        this._tabs = [...this._tabs];
    }

    handleFavoriteSelected(event) {
        this.showLoader();
        const node = event.detail.node;

        console.log('MenuStrumentiContainer::handleFavoriteSelected:: ' + JSON.stringify(node));

        if(this._favorite.length >= 3) {
            this.sendAlert('error', 'Hai raggiunto il numero massimo di 3 preferiti. Per rimuovere i preferiti attuali cliccare nuovamente sulla stella');
            this.hideLoader();
            return;
        }

        const target = this._tabs.find(tab => tab.name === this.currentTab);

        setUserFavorite({
            favLabel: node.label,
            favDevName: node.developerName,
            favType: node.type,
            feiid: node.feiId ? node.feiId : null,
            favParams: node.params ? node.params : null,
            favLink: node.redirectLink ? node.redirectLink : null,
            requestType: node.requestType ? node.requestType : null,
            userContext: target.userContext
        }).catch(error => {
            console.log(JSON.stringify(error))
        }).finally(() => {
            console.log('MenuStrumentiContainer::setUserFavorite call finished');

            if (!Array.isArray(this._favorite)) {
                this._favorite = [];
            }
            
            this._favorite = [...this._favorite, node.developerName];

            console.log('MenuStrumentiContainer::handleFavoriteSelected:: ' + JSON.stringify(this._favorite));

            this.handleFavorite(event);

            this.hideLoader();
            this.sendAlert('success', 'Elemento aggiunto ai preferiti');
        });
    }

    handleUnfavoriteSelected(event) {
        this.showLoader();
        const developerName = event.detail.developerName;

        console.log('MenuStrumentiContainer::handleUnfavoriteSelected:: ' + developerName);

        if(this._favorite.includes(developerName)) {
            this._favorite = this._favorite.filter(name => name !== developerName);
                
            console.log('MenuStrumentiContainer::handleUnfavoriteSelected:: ' + JSON.stringify(this._favorite));
            
            removeUserFavorite({
                favorite: developerName
            }).catch(error => {
                console.log(JSON.stringify(error))
            }).finally(() => {
                console.log('MenuStrumentiContainer::removeUserFavorite call finished');

                this.handleFavorite(event);   
                this.hideLoader();
                this.sendAlert('success', 'Elemento rimosso dai preferiti');
            });
        }
    }

    handleFavorite(event) {
        const developerName = event.detail.developerName;

        // trova la tab attiva
        const target = this._tabs.find(tab => tab.name === this.currentTab);

        // aggiorna solo il nodo corrispondente dentro tutta la struttura
        target.sections = target.sections.map(sec => ({
            ...sec,
            sectionMenus: this.markFavorites(sec.sectionMenus, developerName)
        }));

        this._tabs = [...this._tabs];
    }

    markFavorites(nodes, developerName) {
        return nodes.map(node => {
            // se è il nodo cliccato = togglo il suo isFavorite
            // altrimenti lo lascio com'è
            let fav = false;
            if(developerName) {
                fav = node.developerName === developerName
                                ? !node.isFavorite
                                : node.isFavorite;
            } else {
                fav = this._favorite.includes(node.developerName);
            }

            return {
                ...node,
                isFavorite: fav,
                children: Array.isArray(node.children) && node.children.length
                    ? this.markFavorites(node.children, developerName)
                    : []
            };
        });
    }

    toggleLWCModal() {
        this.isLWCModalOpened = !this.isLWCModalOpened;
    }

    handleNavigateFei(event) {
        const node = event.detail.node;

        this.paramsFEI = {};

        const target = this._tabs.find(tab => tab.name === this.currentTab);

        if(node.type == 'FEI' && node.feiId) {
            console.log('Effettuo FEI normale');
            getParamsForFei({
                feiId : node.feiId,
                userContext: target.userContext
            }).then(fei => {

                const params = node.params ? node.params : (fei.params ? fei.params : null);

                this.paramsFEI.feiId = node.feiId;
                this.paramsFEI.fiscalCode = fei.fiscalCode;
                this.paramsFEI.feiRequestPayload = params
                this.paramsFEI.feiRequestType = node.requestType ? node.requestType : null;
                this.paramsFEI.permissionSetName = fei.permissionSetName;

                this.isLWCModalOpened = true;

            }).catch(error => {
                console.log(JSON.stringify(error))
            }).finally(() => console.log('MenuStrumentiContainer::getSections call finished'));
        } else {
            getUserCf().then(cf => {

                console.log('Effettuo FEI link a servizi');

                if(node.requestType == 'GET') {
                    this.paramsFEI.feiId = 'FEI.LINK.GET';
                    this.paramsFEI.fiscalCode = cf;
                    this.paramsFEI.feiRequestPayload = node.params ? JSON.parse(node.params) : null;
                    this.paramsFEI.feiLink = node.redirectLink;

                } else if(node.requestType == 'POST') {
                    var request = {
                        "properties": JSON.parse(node.params),
                        "requestUrl": node.redirectLink,
                        "requestMethod": node.requestType
                    }

                    console.log('request:: ' + JSON.stringify(request));

                    this.paramsFEI.feiId = 'FEI.LINK.POST';
                    this.paramsFEI.fiscalCode = cf;
                    this.paramsFEI.feiRequestPayload = request;
                    
                    console.log('paramsFEI:: ' + JSON.stringify(this.paramsFEI));
                }

                this.isLWCModalOpened = true;

            }).catch(error => {
                console.log(JSON.stringify(error))
            }).finally(() => console.log('MenuStrumentiContainer::getSections call finished'));
        }
    }

    sendAlert(type, message) {
        const evt = new ShowToastEvent({
            title: '',
            message: message,
            variant: type,
        });
        this.dispatchEvent(evt);
    }
}