global class AccountDetailsNPIBatch implements Database.Batchable<SObject>, Database.Stateful {
    public Date dt;
    private Set<Id> idsSet;
    private Id idValueFrom;
    private Id idValueTo;
    private String dateType;
    private DateTime dateFrom;
    private DateTime dateTo;

    public AccountDetailsNPIBatch(Set<Id> idsSet, Id idValueFrom, Id idValueTo, DateTime dateFrom, DateTime dateTo){
        this.idsSet = idsSet;
        this.idValueFrom = idValueFrom;
        this.idValueTo = idValueTo;
        this.dateFrom = dateFrom;
        this.dateTo = dateTo;
    }

    public AccountDetailsNPIBatch(Object dateGeneric) {
        String stringDate = dateGeneric?.toString();
        try {
            this.dt = Date.valueOf(stringDate);
            return;
        } catch (Exception ex) {
        }
        try {
            this.dt = Date.parse(stringDate);
            return;
        } catch (Exception ex) {
        }
        
    }


    global Database.QueryLocator start(Database.BatchableContext BC) {

        List<String> filters = new List<String>();
            String baseQuery = 'SELECT Id, Name, ExternalId__c, Society__c FROM AccountDetailsNPI__c WHERE ';

            if (dt != null) {
                filters.add('LastModifiedDate >= :dt');
            } else {
                if(this.dateFrom != null){
                    filters.add('LastModifiedDate >= :dateFrom');
                }
                
                if(this.dateTo != null){
                    filters.add('LastModifiedDate <= :dateTo');
                }
            }
            
            if(idsSet != null) {
                filters.add('Id IN :idsSet');
            }
            
            if(idValueFrom != null){
                filters.add('Id >= :idValueFrom');
            }
            
            if(idValueTo != null){
                filters.add('Id <= :idValueTo');
            }

            String fullQuery = filters.isEmpty() ? baseQuery.removeEnd(' WHERE ') : baseQuery + String.join(filters, ' AND ');
            system.debug('Full Query: ' + fullQuery);
            return Database.getQueryLocator(fullQuery);

        // Query ORIGINAL all AccountDetailsNPI__c records for processing
        // return Database.getQueryLocator([
        //     SELECT Id, Name, ExternalId__c, Society__c
        //     FROM AccountDetailsNPI__c
        // ]);
    }

    global void execute(Database.BatchableContext BC, List<AccountDetailsNPI__c> scope) {
        try {
            AccountDetailsNPITriggerHandler handler = new AccountDetailsNPITriggerHandler();
            handler.OnAfterInsert(scope, new Map<Id, AccountDetailsNPI__c>(scope));
        } catch (Exception e) {
            System.debug('Error in execute: ' + e.getMessage());
        }
    }

    global void finish(Database.BatchableContext BC) {
        System.debug('AccountDetailsNPIBatch completed successfully.');
    }
}