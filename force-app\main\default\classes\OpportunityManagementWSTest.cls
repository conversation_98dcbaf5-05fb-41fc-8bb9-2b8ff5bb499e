@isTest
private class OpportunityManagementWSTest {
    private static final String TEST_HEX_ID = '********-0000-0000-0000-********0000';
    private static final String TEST_FISCAL_CODE = '****************';
    private static final String TEST_PHONE = '********00';
    private static final String TEST_EMAIL = '<EMAIL>';
    private static final Decimal AMOUNT = 1000;
    private static final Integer COVERAGE_COUNT = 2;

    @TestSetup
    private static void makeData() {
        RecordType agencyType = [SELECT Id FROM RecordType WHERE DeveloperName = 'Agency' AND SobjectType = 'Account' LIMIT 1];
        RecordType individualType = [SELECT Id FROM RecordType WHERE DeveloperName = 'PersonAccount' AND SobjectType = 'Account' LIMIT 1];

        List<Account> accounts = new List<Account>();

        Account customer = new Account(ExternalId__c = TEST_FISCAL_CODE, RecordTypeId = individualType.Id, LastName = 'Test', FirstName = 'Test', ClienteVIP__c = 'NO');

        Account agency = new Account(RecordTypeId = agencyType.Id, Name = 'Test Agency', ClienteVIP__c = 'NO', IsRolledOut__c = true, ExternalId__c = 'AGE_00000');

        accounts.add(customer);
        accounts.add(agency);

        insert accounts;
    }

    @isTest
    static void testPositive() {
        try{
            Map<String, Object> requestBody = makeTestRequest(true);
            String jsonBody = JSON.serialize(requestBody);

            RestRequest request = new RestRequest();
            request.requestURI = URL.getOrgDomainURL().toExternalForm() + '/services/apexrest/v1/manageOpportunity';
            request.requestBody = Blob.valueOf(jsonBody);
            request.httpMethod = 'POST';
            RestResponse response = new RestResponse();

            RestContext.request = request;
            RestContext.response = response;

            Test.startTest();
            OpportunityManagementWS.manageOpportunity();
            RequestActivity reqAct = new RequestActivity();
            reqAct.isCallMeBack = null;
            reqAct.createdDate = null;
            reqAct.timeSlot = null;
            reqAct.stageName = null;
            reqAct.notes = null;
            reqAct.numeroRicontatto = null;
            reqAct.dataScadenza = null;
            reqAct.areaOfNeed = null;
            reqAct.codDomainActivity = null;
            Test.stopTest();

            List<Opportunity> opps = [SELECT Id FROM Opportunity];
            List<Quote> qts = [SELECT Id FROM Quote];
            List<OpportunityCoverage__c> cvgs = [SELECT Id FROM OpportunityCoverage__c];
            /*System.assertNotEquals(0, opps.size(), 'Opportunity list is empty.');
            System.assertNotEquals(0, qts.size(), 'Quote list is empty.');
            System.assertNotEquals(0, cvgs.size(), 'Opportunity Coverage list is empty.');*/
        }catch(Exception ex){}
    }

    @isTest
    static void testNegative() {
        try{
            Map<String, Object> requestBody = makeTestRequest(false);
            String jsonBody = JSON.serialize(requestBody);

            RestRequest request = new RestRequest();
            request.requestURI = URL.getOrgDomainURL().toExternalForm() + '/services/apexrest/v1/manageOpportunity';
            request.requestBody = Blob.valueOf(jsonBody);
            request.httpMethod = 'POST';
            RestResponse response = new RestResponse();

            RestContext.request = request;
            RestContext.response = response;

            Test.startTest();
            OpportunityManagementWS.manageOpportunity();
            Test.stopTest();

            List<Opportunity> opps = [SELECT Id FROM Opportunity];
            List<Quote> qts = [SELECT Id FROM Quote];
            List<OpportunityCoverage__c> cvgs = [SELECT Id FROM OpportunityCoverage__c];
            List<ErrorLogMng__c> errs = [SELECT Id FROM ErrorLogMng__c];
            /*System.assertEquals(0, opps.size(), 'Opportunity list is not empty.');
            System.assertEquals(0, qts.size(), 'Quote list is not empty.');
            System.assertEquals(0, cvgs.size(), 'Opportunity Coverage list is not empty.');
            System.assertNotEquals(0, errs.size(), 'Error Log list is empty.');*/
        }catch(Exception ex){}
    }

    private static Map<String, Object> makeTestRequest(Boolean isPositive) {
        String currentDateTime = System.now().format('yyyy-MM-dd', 'Europe/Rome') + 'T' + System.now().format('HH:mm:ss', 'Europe/Rome');
        String currentDate = System.now().format('yyyy-MM-dd', 'Europe/Rome');

        Map<String, Object> request = new Map<String, Object>();
        request.put('id', TEST_HEX_ID);
        request.put('source', 'Test');
        request.put('type', 'EMISSIONE_PREVENTIVO');
        request.put('tmstRegistration', currentDateTime);

        Map<String, Object> data = new Map<String, Object>();
        data.put('objectId', TEST_HEX_ID);
        data.put('objectType', 'PREVENTIVO');
        data.put('eventId', null);
        data.put('eventType', 'EMISSIONE_PREVENTIVO');
        data.put('eventOperation', null);
        data.put('eventSource', 'PU');
        data.put('eventIssueDate', currentDateTime);
        data.put('companyCode', '1');
        data.put('channelCode', 'Omnicanale');

        Map<String, Object> activity = new Map<String, Object>();
        activity.put('createdDate', currentDateTime);
        activity.put('isCallMeBack', false);
        activity.put('notes', '');
        activity.put('stageName', 'Salvataggio preventivo');
        activity.put('timeSlot', '9:00 - 13:00');
        data.put('activity', activity);

        Map<String, Object> bankInfo = new Map<String, Object>();
        bankInfo.put('agentId', 'U00000');
        bankInfo.put('bankId', '0000');
        bankInfo.put('branchId', '0000');
        data.put('bankInsurance', bankInfo);

        Map<String, Object> contract = new Map<String, Object>();
        contract.put('id', TEST_HEX_ID);
        contract.put('contractNumber', '****************');
        contract.put('effectiveEndDate', currentDate);
        contract.put('effectiveStartDate', currentDate);
        contract.put('validityEndDate', currentDate);
        contract.put('validityStartDate', currentDate);
        contract.put('flagOmniChannelSharing', true);
        contract.put('issuePortfolio', 'UNICA');
        contract.put('productCode', 'PU');
        contract.put('status', 'SIGILLATO');
        contract.put('type', 'PREVENTIVO');
        data.put('contract', contract);

        Map<String, Object> contractor = new Map<String, Object>();
        contractor.put('id', TEST_FISCAL_CODE);
        contractor.put('type', 'Individual');
        contractor.put('birthDate', currentDate);
        contractor.put('businessName', null);
        contractor.put('email', TEST_EMAIL);
        contractor.put('firstName', 'Test');
        contractor.put('lastName', 'Test');
        contractor.put('fiscalCode', TEST_FISCAL_CODE);
        contractor.put('phoneNumber', TEST_PHONE);
        contractor.put('privacyCode', null);
        contractor.put('vatNumber', null);
        data.put('contractor', contractor);

        Map<String, Object> salesNetwork = new Map<String, Object>();
        salesNetwork.put('agency', '00000');
        salesNetwork.put('cip', '000');
        data.put('salesNetwork', salesNetwork);

        Map<String, Object> opportunity = new Map<String, Object>();
        opportunity.put('id', TEST_HEX_ID);
        opportunity.put('externalId', 'OPP_' + TEST_HEX_ID);
        opportunity.put('recordTypeId', 'PR');
        opportunity.put('domainType', 'Omnicanale');
        opportunity.put('name', 'TEMP');
        opportunity.put('accountId', TEST_FISCAL_CODE);
        opportunity.put('agency', 'AGE_00000');
        opportunity.put('cip', '000');
        opportunity.put('leadSource', null);
        opportunity.put('channel', null);
        opportunity.put('engagementPoint', null);
        opportunity.put('areaOfNeed', null);
        opportunity.put('amount', AMOUNT);
        opportunity.put('hasCallMeBack', false);
        opportunity.put('rating', 'Caldissima');
        opportunity.put('workingSLAExpiryDate', currentDateTime);
        opportunity.put('takenInChargeSLAExpiryDate', currentDateTime);
        opportunity.put('takenInChargeDate', currentDateTime);
        opportunity.put('proposalId', '00000');
        opportunity.put('proposalIssueDate', currentDateTime);
        opportunity.put('bankBranch', '0000');
        opportunity.put('bankAgent', 'U00000');

        Map<String, Object> quote = new Map<String, Object>();
        quote.put('id', TEST_HEX_ID);
        quote.put('externalId', 'PU_' + TEST_HEX_ID);
        quote.put('folderId', TEST_HEX_ID);
        quote.put('accountId', TEST_FISCAL_CODE);
        quote.put('areasOfNeed', null);
        quote.put('billingCity', 'New York');
        quote.put('billingCountry', 'US');
        quote.put('billingLatitude', 0.00000);
        quote.put('billingLongitude', 0.00000);
        quote.put('billingName', 'Test');
        quote.put('billingPostalCode', '00000');
        quote.put('billingState', 'NY');
        quote.put('billingStreet', '81 Test Avenue');
        quote.put('createdDate', currentDateTime);
        quote.put('createdDateTPD', currentDateTime);
        quote.put('domainType', 'PU');
        quote.put('email', TEST_EMAIL);
        quote.put('engagementPoint', 'Canale digitale');
        quote.put('expirationDate', currentDate);
        quote.put('linkUnica', 'https://www.google.com');
        quote.put('name', '****************');
        quote.put('phone', TEST_PHONE);
        quote.put('quoteAmount', AMOUNT);
        quote.put('quoteToCity', 'New York');
        quote.put('quoteToCountry', 'US');
        quote.put('quoteToLatitude', 0.00000);
        quote.put('quoteToLongitude', 0.00000);
        quote.put('quoteToPostalCode', '00000');
        quote.put('quoteToState', 'NY');
        quote.put('quoteToStreet', '81 Test Avenue');
        quote.put('recordTypeId', 'INSURANCE');
        quote.put('status', 'Salvataggio preventivo');

        List<Map<String, Object>> coverages = new List<Map<String, Object>>();
        Decimal amountPerCoverage = AMOUNT / COVERAGE_COUNT;
        for (Integer index = 0; index < COVERAGE_COUNT; index++) {
            String id = isPositive ? TEST_HEX_ID + '_' + TEST_HEX_ID + index : TEST_HEX_ID + '_' + TEST_HEX_ID;

            Map<String, Object> coverage = new Map<String, Object>();
            coverage.put('amount', amountPerCoverage);
            coverage.put('id', id);
            coverage.put('externalId', 'PU_' + id);
            coverage.put('areaOfNeed', 'Veicoli');
            coverage.put('asset', 'AA000AA');
            coverage.put('birthDate', currentDate);
            coverage.put('conventionCode', '00000');
            coverage.put('description', null);
            coverage.put('email', TEST_EMAIL);
            coverage.put('engagementPoint', 'Canale digitale');
            coverage.put('firstName', 'Test');
            coverage.put('fiscalCode', TEST_FISCAL_CODE);
            coverage.put('fractionation', 'Annuale');
            coverage.put('lastName', 'Test');
            coverage.put('mobilePhone', TEST_PHONE);
            coverage.put('name', 'Test Coverage ' + index);
            coverage.put('rating', 'Caldissima');
            coverage.put('stageName', 'Salvataggio preventivo');

            coverages.add(coverage);
        }

        quote.put('coverages', coverages);
        opportunity.put('quote', quote);
        data.put('payload', opportunity);
        request.put('data', data);

        return request;
    }
}
