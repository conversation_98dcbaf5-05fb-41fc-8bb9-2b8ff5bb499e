<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <assignments>
        <name>caseAssignee</name>
        <label>caseAssignee</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>assignTo</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.AssignedTo__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>assignToGroup</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>getAssignToGroupDets.PublicGroupId__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseNum</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.CaseNumber</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>getMetaDataRec</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>caseAssignTo</name>
        <label>caseAssignTo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>assignTo</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.AssignedTo__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseNum</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.CaseNumber</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>getMetaDataRec</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>caseAssignToGroup</name>
        <label>caseAssignToGroup</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>assignToGroup</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>getAssignToGroupDets.PublicGroupId__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseNum</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.CaseNumber</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>getMetaDataRec</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_1_of_caseAssignee</name>
        <label>Copy 1 of caseAssignee</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>assignTo</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.AssignedTo__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>assignToGroup</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>getAssignToGroupDets.PublicGroupId__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseNum</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.CaseNumber</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>getMetaDataRec</targetReference>
        </connector>
    </assignments>
    <collectionProcessors>
        <name>metadataFilterConditionCreation</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>metadataFilterConditionCeration</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNextValueToReference>currentItem_metadataFilterConditionCreation</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>getMetaDataRec</collectionReference>
        <conditionLogic>(1 AND 2 AND 3 AND 5) OR (1 AND 2 AND 4 AND 5)</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_metadataFilterConditionCreation.Notification_Name__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>Creation</stringValue>
            </rightValue>
        </conditions>
        <conditions>
            <leftValueReference>currentItem_metadataFilterConditionCreation.Object__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>Case</stringValue>
            </rightValue>
        </conditions>
        <conditions>
            <leftValueReference>currentItem_metadataFilterConditionCreation.To_Whom__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>AssignedTo</stringValue>
            </rightValue>
        </conditions>
        <conditions>
            <leftValueReference>currentItem_metadataFilterConditionCreation.To_Whom__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>AssignedGroup</stringValue>
            </rightValue>
        </conditions>
        <conditions>
            <leftValueReference>currentItem_metadataFilterConditionCreation.Channel__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>Bell</stringValue>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>metadataFilterConditionReassign</targetReference>
        </connector>
    </collectionProcessors>
    <collectionProcessors>
        <name>metadataFilterConditionReassign</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>metadataFilterConditionReassign</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNextValueToReference>currentItem_metadataFilterConditionCreation</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>getMetaDataRec</collectionReference>
        <conditionLogic>(1 AND 2 AND 3 AND 5) OR (1 AND 2 AND 4 AND 5)</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_metadataFilterConditionCreation.Notification_Name__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>Reassignment</stringValue>
            </rightValue>
        </conditions>
        <conditions>
            <leftValueReference>currentItem_metadataFilterConditionCreation.Object__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>Case</stringValue>
            </rightValue>
        </conditions>
        <conditions>
            <leftValueReference>currentItem_metadataFilterConditionCreation.To_Whom__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>AssignedTo</stringValue>
            </rightValue>
        </conditions>
        <conditions>
            <leftValueReference>currentItem_metadataFilterConditionCreation.To_Whom__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>AssignedGroup</stringValue>
            </rightValue>
        </conditions>
        <conditions>
            <leftValueReference>currentItem_metadataFilterConditionCreation.Channel__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>Bell</stringValue>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>metadataValidation</targetReference>
        </connector>
    </collectionProcessors>
    <decisions>
        <name>assignToOrAssignToGroup</name>
        <label>assignToOrAssignToGroup</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>getMetaDataRec</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>whileCreation</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record__Prior.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>caseAssignee</targetReference>
            </connector>
            <label>whileCreation</label>
        </rules>
        <rules>
            <name>updationAssignTo</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record__Prior.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.AssignedTo__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.AssignedGroup__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>caseAssignTo</targetReference>
            </connector>
            <label>updationAssignTo</label>
        </rules>
        <rules>
            <name>updationAssignToGroup</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record__Prior.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.AssignedGroup__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.AssignedTo__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>caseAssignToGroup</targetReference>
            </connector>
            <label>updationAssignToGroup</label>
        </rules>
        <rules>
            <name>updationAssignToAndAssignToGroup</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record__Prior.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.AssignedTo__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.AssignedGroup__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_caseAssignee</targetReference>
            </connector>
            <label>updationAssignToAndAssignToGroup</label>
        </rules>
    </decisions>
    <decisions>
        <name>metadataValidation</name>
        <label>metadataValidation</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>validMetadataCreation</name>
            <conditionLogic>1 AND (2 OR 3) AND 4 AND 5</conditionLogic>
            <conditions>
                <leftValueReference>metadataFilterConditionCreation</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>assignTo</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>assignToGroup</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Label.PushNotificationLeoActivityCodeCheck</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Record.LeoActivityCode__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>sendCustomNotificationFlowCreation</targetReference>
            </connector>
            <label>validMetadataCreation</label>
        </rules>
        <rules>
            <name>validMetadataReassign</name>
            <conditionLogic>1 AND (2 OR 3) AND 4 AND 5</conditionLogic>
            <conditions>
                <leftValueReference>metadataFilterConditionReassign</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>assignTo</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>assignToGroup</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Label.PushNotificationLeoActivityCodeCheck</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>$Record.LeoActivityCode__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>sendCustomNotificationFlowReassign</targetReference>
            </connector>
            <label>validMetadataReassign</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Notification {!$Flow.CurrentDateTime}</interviewLabel>
    <label>NotificationManagement</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>getAssignToGroupDets</name>
        <label>getAssignToGroupDets</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>assignToOrAssignToGroup</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.AssignedGroup__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getCustomNotificationTypeRecCreation</name>
        <label>getCustomNotificationTypeRecCreation</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>getCustomNotificationTypeRecReasign</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CustomNotifTypeName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Creation</stringValue>
            </value>
        </filters>
        <object>CustomNotificationType</object>
        <outputAssignments>
            <assignToReference>customNotificationNameCreation</assignToReference>
            <field>CustomNotifTypeName</field>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>customNotificationIdCreation</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <name>getCustomNotificationTypeRecReasign</name>
        <label>getCustomNotificationTypeRecReasign</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>metadataFilterConditionCreation</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CustomNotifTypeName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Reassignment</stringValue>
            </value>
        </filters>
        <object>CustomNotificationType</object>
        <outputAssignments>
            <assignToReference>customNotificationTypeReassign</assignToReference>
            <field>CustomNotifTypeName</field>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>customNotificationIdReassign</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <name>getMetaDataRec</name>
        <label>getMetaDataRec</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>getCustomNotificationTypeRecCreation</targetReference>
        </connector>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>globalNotificationSchema__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>getAssignToGroupDets</targetReference>
        </connector>
        <object>Case</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <name>sendCustomNotificationFlowCreation</name>
        <label>sendCustomNotificationFlowCreation</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <flowName>notificationSystemOrchestrator</flowName>
        <inputAssignments>
            <name>assignTo</name>
            <value>
                <elementReference>assignTo</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>assignToGroup</name>
            <value>
                <elementReference>assignToGroup</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>caseNumber</name>
            <value>
                <elementReference>caseNum</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>notType</name>
            <value>
                <elementReference>customNotificationNameCreation</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>notTypeId</name>
            <value>
                <elementReference>customNotificationIdCreation</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varTargetRecordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <subflows>
        <name>sendCustomNotificationFlowReassign</name>
        <label>sendCustomNotificationFlowReassign</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <flowName>notificationSystemOrchestrator</flowName>
        <inputAssignments>
            <name>assignTo</name>
            <value>
                <elementReference>assignTo</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>assignToGroup</name>
            <value>
                <elementReference>assignToGroup</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>caseNumber</name>
            <value>
                <elementReference>caseNum</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>notType</name>
            <value>
                <elementReference>customNotificationTypeReassign</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>notTypeId</name>
            <value>
                <elementReference>customNotificationIdReassign</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varTargetRecordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <variables>
        <name>assignTo</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>assignToGroup</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>caseNum</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>currentItem_metadataFilterConditionCreation</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>globalNotificationSchema__mdt</objectType>
    </variables>
    <variables>
        <name>customNotificationIdCreation</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>customNotificationIdReassign</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>customNotificationNameCreation</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>customNotificationTypeReassign</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>testVals</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
</Flow>
