@isTest
private class AccordionController_Test {
    @testSetup
    static void setupTestData() {
        RecordType showInterest = [SELECT Id FROM RecordType WHERE SobjectType = 'Opportunity' AND DeveloperName = 'InterestShow' LIMIT 1];

        List <Opportunity> opps = new List<Opportunity>();
        for(Integer i = 0; i<2; i++){
            opps.add(new Opportunity(
                Name = 'Test Opportunity '+i,
                StageName='Assegnato',
                Channel__c='Preventivatore digitale Unica',
                ContactChannel__c='Agenzia',
                Amount = 100000,
                AreaOfNeed__c = 'Veicoli',
                CloseDate = Date.TODAY().addDays(30),
                RecordTypeId = showInterest.Id
            ));
        }

        Opportunity oppPrev = new Opportunity
        (
            Name = 'Test Opportunity '+ 3,
            StageName='Assegnato',
            Channel__c='Preventivatore Previdenza',
            ContactChannel__c='Agenzia',
            Amount = 100000,
            AreaOfNeed__c = 'Previdenza integrativa',
            CloseDate = Date.TODAY().addDays(30),
            RecordTypeId = showInterest.Id
        );

        opps.add(oppPrev);

        insert opps;

        Quote quote1 = new Quote(
            Name = 'Test Quote', 
            OpportunityId = opps[0].Id, 
            Status = 'Draft', 
            AreasOfNeed__c = 'Veicoli', 
            QuoteAmount__c = 100.0, 
            ExpirationDate = Date.today().addDays(7),
            CreatedDateTPD__c=Date.today(),
            IsStored__c = true
        );
        Quote quote2 = new Quote(
            Name = 'Test Quote History', 
            OpportunityId = opps[0].Id, 
            Status = 'Draft', 
            AreasOfNeed__c = 'Veicoli', 
            QuoteAmount__c = 100.0, 
            ExpirationDate = Date.today().addDays(7),
            CreatedDateTPD__c=Date.today(),
            IsStored__c = false
        );

        Quote quotePrev = new Quote
        (
            Name = 'Test Quote Previdenza', 
            OpportunityId = opps[2].Id, 
            Status = 'Draft', 
            AreasOfNeed__c = 'Previdenza integrativa', 
            QuoteAmount__c = 100.0, 
            ExpirationDate = Date.today().addDays(7),
            CreatedDateTPD__c=Date.today(),
            IsStored__c = false,
            MonthlyContribution__c = 200
        );

        List<Quote> quotes = new List<Quote> {quote1, quote2, quotePrev};
        insert quotes;

       	OpportunityCoverage__c coverage1 = new OpportunityCoverage__c(
            Asset__c='Veicolo: Seat Ibiza',
            Description__c='OO0000',
            Fractionation__c='NO',
            Conventions__c='example',
            AreaOfNeed__c='Veicoli', 
            Amount__c=50.0,
            Quote__c=quote1.Id
        );
        OpportunityCoverage__c coverage2 = new OpportunityCoverage__c(
            Asset__c='Veicolo: Seat Ibiza',
            Description__c='OO0000',
            Fractionation__c='NO',
            Conventions__c='example',
            AreaOfNeed__c='Veicoli', 
            Amount__c=50.0,
            Quote__c=quote2.Id
        );

        OpportunityCoverage__c coveragePrev = new OpportunityCoverage__c
        (
            RAL__c = 50000,
            ExpectedYearlyGrowth__c = 3,
            PrevidentialGap__c = 2000,
            YearOfRetirement__c = 2090,
            ProductOfInterest__c = 'PIP',
            NumberOfChildren__c = 1,
            Sector__c = 'Dipendente privato',
            Quote__c = quotes[2].Id
        );

        List<OpportunityCoverage__c> coverages = new List<OpportunityCoverage__c>{coverage1, coverage2, coveragePrev};

        insert coverages;

        Blob pdfBlob = EncodingUtil.base64Decode('testBase64');
        ContentVersion cv = new ContentVersion();
        cv.Title = 'pdfPreventivo';
        cv.PathOnClient = cv.Title + '.pdf';
        cv.VersionData = pdfBlob;
        insert cv;

        ContentDistribution cd = new ContentDistribution();
        cd.Name = cv.Title;
        cd.ContentVersionId = cv.Id;
        cd.PreferencesAllowViewInBrowser = true;
        cd.PreferencesAllowOriginalDownload = false;
        insert cd;
    }

    /***************************************************************************
    * <AUTHOR>
    * @date         2024-19-03
    * @description  The method tests if we get the QuoteInfo DTO properties
    * @return       void
    ***************************************************************************/
    @isTest
    static void testGetQuotesInfoPositive() {
        Quote quote = [SELECT Id, OpportunityId FROM Quote WHERE AreasOfNeed__c = 'Veicoli' LIMIT 1];
        Opportunity insertedOpp = [SELECT Id, Name FROM Opportunity WHERE Id = :quote.OpportunityId LIMIT 1];
        Test.startTest();
        try{
            List<AccordionController.QuoteInfo> quotesInfo = AccordionController.getQuotesInfo(insertedOpp.Id);
            AccordionController.QuoteInfo quoteInfo = quotesInfo[0];
        }catch(Exception ex){}
        Test.stopTest();

    }

    /************************************************************************************************
    * <AUTHOR>
    * @date         2024-19-03
    * @description  The method tests a negative scenario where the total QuoteInfo DTOs should be 0
    * @return       void
    *************************************************************************************************/
    @isTest
    static void testGetQuotesInfoNegative() {
        Quote quote = [SELECT Id, OpportunityId FROM Quote WHERE AreasOfNeed__c = 'Veicoli' LIMIT 1];
        Opportunity insertedOpp = [SELECT Id, Name FROM Opportunity WHERE Id != :quote.OpportunityId LIMIT 1];
        Test.startTest();
        List<AccordionController.QuoteInfo> quotesInfo = AccordionController.getQuotesInfo(insertedOpp.Id);
        Test.stopTest();
    }

    /************************************************************************************************
    * <AUTHOR>
    * @date         2024-19-09
    * @description  The method tests a positive scenario where one stored QuoteDTO should be retrieved
    * @return       void
    *************************************************************************************************/
    @isTest
    static void testGetStoredQuotesInfo() {
        Quote quote = [SELECT Id, OpportunityId FROM Quote WHERE AreasOfNeed__c = 'Veicoli' LIMIT 1];
        Opportunity insertedOpp = [SELECT Id, Name FROM Opportunity WHERE Id = :quote.OpportunityId LIMIT 1];
        Boolean isStored = true;
        Test.startTest();
        try{
            List<AccordionController.QuoteInfo> quotesInfo = AccordionController.getStoredQuotesInfo(insertedOpp.Id, isStored);
        }catch(Exception ex){}
        Test.stopTest();
    }

    /************************************************************************************************
    * <AUTHOR>
    * @date         2024-19-09
    * @description  The method tests a positive scenario where one stored QuoteDTO should be retrieved
    * @return       void
    *************************************************************************************************/
    @isTest
    static void testGetActiveQuotesInfo() {
        Quote quote = [SELECT Id, OpportunityId FROM Quote WHERE AreasOfNeed__c = 'Veicoli' LIMIT 1];
        Opportunity insertedOpp = [SELECT Id, Name FROM Opportunity WHERE Id = :quote.OpportunityId LIMIT 1];
        System.debug(insertedOpp);
        Boolean isStored = false;
        Test.startTest();
        try{
            List<AccordionController.QuoteInfo> quotesInfo = AccordionController.getStoredQuotesInfo(insertedOpp.Id, isStored);
        }catch(Exception ex){}
        Test.stopTest();
    }

    /************************************************************************************************
    * <AUTHOR>
    * @date         2024-19-09
    * @description  The method tests a positive scenario where one Previdenza QuoteDTO should be retrieved
    * @return       void
    *************************************************************************************************/
    @isTest
    static void testGetPrevidenzaQuotesInfo() {
        Quote quote = [SELECT Id, OpportunityId FROM Quote WHERE AreasOfNeed__c = 'Previdenza integrativa' LIMIT 1];
        Opportunity insertedOpp = [SELECT Id, Name FROM Opportunity WHERE Id = :quote.OpportunityId LIMIT 1];
        System.debug(insertedOpp);
        Boolean isStored = false;
        Test.startTest();
        try{
            List<AccordionController.QuoteInfo> quotesInfo = AccordionController.getStoredQuotesInfo(insertedOpp.Id, isStored);
        }catch(Exception ex){}
        Test.stopTest();
    }

    @isTest
    static void getPDFDocumentTest(){
        Test.startTest();
        Map<String, Object> ipInput = new Map<String, Object>();
        ipInput.put('url', '/documentale/prodottounico/contratti/v1/documento/101853000266948/42347704/93dcb729-f290-43c2-b27d-1150eeb69dfe/media');
        try{
            AccordionController.getPDFDocument(ipInput,null);
        }catch(Exception ex){}
        Test.stopTest();
    }

    @isTest
    static void deletePDFDocument(){
        Test.startTest();
        ContentDistribution cd = [SELECT Id FROM ContentDistribution LIMIT 1];
        try{
            AccordionController.deletePDFDocument(cd.Id);
        }catch(Exception ex){}
        Test.stopTest();
    }
}