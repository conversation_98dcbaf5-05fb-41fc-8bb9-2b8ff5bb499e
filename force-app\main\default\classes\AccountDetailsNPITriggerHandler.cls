public with sharing class AccountDetailsNPITriggerHandler {
    /**
     * @description     This method handles the After Insert trigger event.
     * @param newList   A list of the new version of the Account Account Relations.
     */
    public void onAfterInsert(List<AccountDetailsNPI__c> newList, Map<Id, AccountDetailsNPI__c> newMap) {
        // SVILUPPI ANTONINO
        /*try {
            // Query the input records to ensure lookup fields are navigable
            List<AccountDetailsNPI__c> queriedAccountDetailsNPIRecords = [
                SELECT Id, Name, ExternalId__c, Society__c
                FROM AccountDetailsNPI__c
                WHERE Id IN :newList
            ];
            System.debug('Queried AccountDetailsNPI__c records: ' + queriedAccountDetailsNPIRecords);

            // Collect IDs of AccountDetailsNPI__c records
            Set<Id> accountDetailsNPIIds = new Set<Id>();
            // Identify the company (Society) and the client (Subject)
            Set<Id> companyIds = new Set<Id>(); //Company Id
            Map<Id, Id> societyDetailNpiMap = new Map<Id, Id>(); //Map to hold Society Id and AccountDetailsNPI__c Id
            for (AccountDetailsNPI__c accountDetail : queriedAccountDetailsNPIRecords) {
                accountDetailsNPIIds.add(accountDetail.Id);
                if (accountDetail.Society__c != null) {
                    companyIds.add(accountDetail.Society__c);
                    societyDetailNpiMap.put(accountDetail.Society__c, accountDetail.Id); //TODO Map<Id, Id> --> Map<Id, List<Id>>
                }
            }
            System.debug('Collected AccountDetailsNPI__c IDs: ' + accountDetailsNPIIds);
            System.debug('Collected Company IDs: ' + companyIds);

            // Query AAR records representing relation between Account(Client/Subject) and Society
            List<FinServ__AccountAccountRelation__c> accountSocietyAAR = [
                SELECT Id, FinServ__Account__c, FinServ__RelatedAccount__c, FinServ__RelatedAccount__r.ExternalId__c, FinServ__Account__r.ExternalId__c
                FROM FinServ__AccountAccountRelation__c
                WHERE FinServ__RelatedAccount__c IN :companyIds AND RecordType.DeveloperName = 'AccountSociety' AND FinServ__relatedAccount__r.RecordType.DeveloperName = 'Society' AND FinServ__Account__r.RecordType.DeveloperName IN ('Account', 'IndustriesBusiness', 'PersonAccount')
            ];
            System.debug('Queried AAR AccountSociety: ' + accountSocietyAAR);

            // Identify the account/client (Subject)
            Set<Id> subjectIds = new Set<Id>(); //subject Id
            for (FinServ__AccountAccountRelation__c accountSocietyAARElement : accountSocietyAAR) {
                if (accountSocietyAARElement.FinServ__Account__r != null) {
                    subjectIds.add(accountSocietyAARElement.FinServ__Account__c);
                }
            }
            System.debug('Collected subjectIds IDs: ' + subjectIds);

            // Query AAR records representing relation between Account(Client/Subject) and Agency
            List<FinServ__AccountAccountRelation__c> accountAgencyAAR = [
                SELECT Id, FinServ__Account__c, FinServ__RelatedAccount__c, FinServ__RelatedAccount__r.ExternalId__c, FinServ__Account__r.ExternalId__c
                FROM FinServ__AccountAccountRelation__c
                WHERE FinServ__Account__c IN :subjectIds AND RecordType.DeveloperName = 'AccountAgency' AND FinServ__relatedAccount__r.RecordType.DeveloperName = 'Agency'
            ];
            System.debug('Queried AAR AccountAgency: ' + accountAgencyAAR);

            // Identify the agency correlated to Subject of Society
            Set<Id> agencyds = new Set<Id>(); //agency Id
            for (FinServ__AccountAccountRelation__c accountAgencyAARElement : accountAgencyAAR) {
                if (accountAgencyAARElement.FinServ__RelatedAccount__c != null) {
                    agencyds.add(accountAgencyAARElement.FinServ__RelatedAccount__c);
                }
            }
            System.debug('Collected Agency IDs: ' + agencyds);

            // Query AAR records representing mandates between Agency and Society
            List<FinServ__AccountAccountRelation__c> agencyMandates = [
                SELECT Id, FinServ__Account__c, FinServ__RelatedAccount__c, FinServ__RelatedAccount__r.ExternalId__c, FinServ__Account__r.ExternalId__c
                FROM FinServ__AccountAccountRelation__c
                WHERE FinServ__RelatedAccount__c IN :companyIds AND FinServ__Account__c IN :agencyds AND RecordType.DeveloperName = 'AgencySociety'
            ];
            System.debug('Queried Agency mandates: ' + agencyMandates);

            // Collect ExternalId values for Groups corresponding to Agencies
            Set<String> agencyGroupCodes = new Set<String>();
            for (FinServ__AccountAccountRelation__c mandate : agencyMandates) {
                if (mandate.FinServ__Account__r.ExternalId__c != null) {
                    agencyGroupCodes.add('R_' + mandate.FinServ__Account__r.ExternalId__c);
                }
            }
            System.debug('Generated Agency Group Codes: ' + agencyGroupCodes);

            // Query Groups based on DeveloperName
            Map<String, Id> groupMap = new Map<String, Id>();
            List<Group> groups = [
                SELECT Id, DeveloperName
                FROM Group
                WHERE DeveloperName IN :agencyGroupCodes
            ];
            for (Group groupElement : groups) {
                groupMap.put(groupElement.DeveloperName, groupElement.Id);
            }
            System.debug('Queried Groups: ' + groupMap);

            // Create Apex Sharing records for each group
            List<AccountDetailsNPI__Share> sharesToInsert = new List<AccountDetailsNPI__Share>();
            Set<String> sharedElement = new Set<String>();
            for (FinServ__AccountAccountRelation__c agencyMandateElement : agencyMandates) {
                Id societyId = agencyMandateElement.FinServ__RelatedAccount__c; ///////////-----------<*****
                System.debug('Check Society: ' + agencyMandateElement.FinServ__RelatedAccount__c);
                if (societyId != null) {
                    for (String groupCode : agencyGroupCodes) {
                        if (groupMap.containsKey(groupCode)) {
                            AccountDetailsNPI__Share share = new AccountDetailsNPI__Share();
                            share.ParentId = societyDetailNpiMap.get(societyId);
                            share.UserOrGroupId = groupMap.get(groupCode);
                            share.AccessLevel = 'Read';
                            sharesToInsert.add(share);
                            System.debug('Prepared Apex Sharing record for Group: ' + groupCode + ', AccountDetailsNPI__c: ' + societyDetailNpiMap.get(societyId));
                        }
                    }
                }
            }

            // Insert Apex Sharing records
            if (Schema.sObjectType.AccountDetailsNPI__Share.isCreateable()) {
                if (!sharesToInsert.isEmpty()) {
                    insert sharesToInsert;
                    System.debug('Inserted Apex Sharing records successfully.');
                } else {
                    System.debug('No Apex Sharing records to insert.');
                }
            } else {
                System.debug('User does not have permission to create AccountDetailsNPI__Share records.');
            }
        } catch (Exception e) {
            System.debug('Error in onAfterInsert: ' + e.getMessage());
        }*/

        System.debug('***** ');
        //22.06.2025
        try {
            // [MC] Utilità: per ogni account, lista agenzie con cui account ha relazione
            Map<Id, Set<Id>> accountAgencyMap = new Map<Id, Set<Id>>();
            Map<Id, AgencyMetadata> agencyMetadataMap = new Map<Id, AgencyMetadata>();

            // [MC]
            // Lista sharing rule per upsert
            List<AccountDetailsNPI__Share> sharingRuleList = new List<AccountDetailsNPI__Share>();

            Map<Id, String> mapIdAgencyWithAgeName = new Map<Id, String>();

            Map<Id, Set<Id>> mapAccountDetailsNPIWithAccountId = new Map<Id, Set<Id>>();
            Map<Id, Set<Id>> mapAccountDetailsNPIWithSocietyId = new Map<Id, Set<Id>>();

            //per loro natura, da quanto capito, sui record AccountDetailsNPI__c ci sono(o ci si aspetta sempre) relazioni (relation__c) tra account
            //di tipo(rectype) agenzie(Agency) e account di tipo(rectype) society(società)
            //quindi la mappa "mapAccountDetailsNPIWithAccountId" che sotto viene popolata opportunamente,
            // conterrà come id gli id dei record AccountDetailsNPI__C e come values quelli delle agenzie
            for (AccountDetailsNPI__c npi : [
                SELECT Relation__r.FinServ__Account__c, Relation__r.FinServ__RelatedAccount__c
                FROM AccountDetailsNPI__c
                WHERE Id IN :newMap.keySet()
            ]) {
                if (!mapAccountDetailsNPIWithAccountId.containsKey(npi.Id)) {
                    mapAccountDetailsNPIWithAccountId.put(npi.Id, new Set<Id>());
                }
                mapAccountDetailsNPIWithAccountId.get(npi.Id).add(npi.Relation__r.FinServ__Account__c);
                if (!mapAccountDetailsNPIWithSocietyId.containsKey(npi.Id)) {
                    mapAccountDetailsNPIWithSocietyId.put(npi.Id, new Set<Id>());
                }
                mapAccountDetailsNPIWithSocietyId.get(npi.Id).add(npi.Relation__r.FinServ__RelatedAccount__c);
            }
            System.debug('***** AccountDetailsNPI__c.onAfterInsert: mapAccountDetailsNPIWithAccountId= ' + mapAccountDetailsNPIWithAccountId);
            Set<Id> setAllIds = extractAllIdsFromMap(mapAccountDetailsNPIWithAccountId);
            System.debug('***** AccountDetailsNPI__c.onAfterInsert: setAllIds= ' + setAllIds);
            for (FinServ__AccountAccountRelation__c relation : [
                SELECT FinServ__Account__c, FinServ__RelatedAccount__c
                FROM FinServ__AccountAccountRelation__c
                WHERE RecordType.DeveloperName = 'AccountAgency' AND FinServ__Account__c IN :setAllIds
            ]) {
                if (!accountAgencyMap.containsKey(relation.FinServ__Account__c)) {
                    accountAgencyMap.put(relation.FinServ__Account__c, new Set<Id>());
                }
                accountAgencyMap.get(relation.FinServ__Account__c).add(relation.FinServ__RelatedAccount__c);
            }
            System.debug('***** AccountDetailsNPI__c.onAfterInsert: accountAgencyMap= ' + accountAgencyMap);

            system.debug('***** AccountDetailsNPI__c.onAfterInsert: mapAccountDetailsNPIWithAccountId= ' + mapAccountDetailsNPIWithAccountId);
            // Set<Id> setAllIdsFromAgencyMap = extractAllIdsFromMap(mapAccountDetailsNPIWithAccountId);
            Set<Id> setAllIdsFromAgencyMap = extractAllIdsFromMap(accountAgencyMap);
            system.debug( '***** AccountDetailsNPI__c.onAfterInsert: setAllIdsFromAgencyMap= ' + setAllIdsFromAgencyMap); 

            for (FinServ__AccountAccountRelation__c relation : [
                SELECT FinServ__Account__c, FinServ__RelatedAccount__c, FinServ__Account__r.ExternalId__c
                FROM FinServ__AccountAccountRelation__c
                WHERE RecordType.DeveloperName = 'AgencySociety' AND FinServ__Account__c IN :setAllIdsFromAgencyMap
            ]) { 
                AgencyMetadata agencyMetadata = agencyMetadataMap.containsKey(relation.FinServ__Account__c) ? agencyMetadataMap.get(relation.FinServ__Account__c) : new AgencyMetadata();
                agencyMetadata.idAgency = relation.FinServ__Account__c;
                //TODO
                //agencyMetadata.visibilitaTotaleGroup = ConiHelper.getGruppoVisibilitaTotaleAgenzia(agency);
                if (agencyMetadata.mandatoList == null) {
                    agencyMetadata.mandatoList = new List<Id>();
                }
                agencyMetadata.mandatoList.add(relation.FinServ__RelatedAccount__c);
                agencyMetadataMap.put(relation.FinServ__Account__c, agencyMetadata);
                System.debug('***** AccountDetailsNPI__c.onAfterInsert: relation.FinServ__Account__c = ' + relation.FinServ__Account__c);
                System.debug('***** AccountDetailsNPI__c.onAfterInsert:  relation.FinServ__Account__r.ExternalId__c = ' + relation.FinServ__Account__r.ExternalId__c);
                
                mapIdAgencyWithAgeName.put(relation.FinServ__Account__c, System.Label.AgencyExtIdAAR + relation.FinServ__Account__r.ExternalId__c);
            }
            System.debug('mapIdAgencyWithAgeName: '+mapIdAgencyWithAgeName);


            for (Group gr : [SELECT Id, DeveloperName FROM Group WHERE DeveloperName IN :mapIdAgencyWithAgeName.values()]) {
                for (Id idAgency : mapIdAgencyWithAgeName.keySet()) {
                    if (mapIdAgencyWithAgeName.get(idAgency) == gr.DeveloperName) {
                        AgencyMetadata agencyMetadata = agencyMetadataMap.get(idAgency);
                        agencyMetadata.idVisibilitaTotaleGroup = gr.Id;
                        agencyMetadataMap.put(idAgency, agencyMetadata);
                    }
                }
            }
            System.debug('***** AccountDetailsNPI__c.onAfterInsert: agencyMetadataMap=' + agencyMetadataMap);

            //TODO FOR SU NPI PER POI CREARE LA SHARE
            for (Id idAgency : agencyMetadataMap.keySet()) {
                AgencyMetadata agencyMetadata = agencyMetadataMap.get(idAgency);
                for (Id idMandato : agencyMetadata.mandatoList) {
                    for (Id npi : mapAccountDetailsNPIWithSocietyId.keySet()) {
                        if (mapAccountDetailsNPIWithSocietyId.get(npi).contains(idMandato)) {
                            AccountDetailsNPI__Share share = new AccountDetailsNPI__Share();
                            share.ParentId = npi;
                            share.UserOrGroupId = agencyMetadata.idVisibilitaTotaleGroup;
                            share.AccessLevel = 'Read';
                            sharingRuleList.add(share);
                        }
                    }
                }
            }
            insert sharingRuleList;
        } catch (Exception e) {
            System.debug('ERROR - AccountDetailsNPI__c.onAfterInsert: stackTrace=' + e.getStackTraceString() + ', message=' + e.getMessage());
        }
        System.debug('***** AccountDetailsNPI__c.onAfterInsert: end');
    }

    public class AgencyMetadata {
        public Id idAgency;
        public Id idVisibilitaTotaleGroup;
        public List<Id> mandatoList;
    }

    // Metodo di utilità per estrarre tutti gli Id da una Map<Id, Set<Id>>
    // Restituisce un Set<Id> con tutti gli Id unici trovati nei valori della mappa
    public static Set<Id> extractAllIdsFromMap(Map<Id, Set<Id>> inputMap) {
        Set<Id> result = new Set<Id>();
        for (Set<Id> idSet : inputMap.values()) {
            result.addAll(idSet);
        }
        return result;
    }
}