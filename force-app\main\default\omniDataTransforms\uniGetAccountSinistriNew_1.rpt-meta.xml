<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <name>uniGetAccountSinistriNew</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem14</globalKey>
        <inputFieldName>accAccRelation:FinServ__RelatedAccount__r.AgencyCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Account:societa</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;SOC_1&quot; : &quot;1&quot;,
  &quot;SOC_4&quot; : &quot;4&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem13</globalKey>
        <inputFieldName>UniSalute</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>permissionSetAssignmentUnisalute</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem12</globalKey>
        <inputFieldName>UnipolSai</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>permissionSetAssignmentUnipolSai</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem11</globalKey>
        <inputFieldName>Account:SourceSocietyDescription__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountSociety</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem18</globalKey>
        <inputFieldName>accAccRelation:FinServ__Account__r.ExternalId__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>CF</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem17</globalKey>
        <inputFieldName>AccountSoc:FinServ__RelatedAccount__r.AgencyCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Account:societa</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;SOC_1&quot; : &quot;1&quot;,
  &quot;SOC_4&quot; : &quot;4&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem16</globalKey>
        <inputFieldName>AccountSoc:FinServ__RelatedAccount__r.Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Account:nomeSocieta</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem15</globalKey>
        <inputFieldName>AccountSoc:Identifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>Account:codiceAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;AgencySociety&apos;</filterValue>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem6</globalKey>
        <inputFieldName>RecordType.DeveloperName</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountSoc</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>User:IdAzienda__c</filterValue>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem5</globalKey>
        <inputFieldName>FinServ__Account__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountSoc</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;AccountSociety&apos;</filterValue>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem4</globalKey>
        <inputFieldName>RecordType.DeveloperName</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>accAccRelation</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:permissionSetAssignmentUniSalute LISTSIZE 0 &gt;</formulaConverted>
        <formulaExpression>LISTSIZE(permissionSetAssignmentUniSalute)&gt;0</formulaExpression>
        <formulaResultPath>UniSalute</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem3</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem10</globalKey>
        <inputFieldName>Account:VatNumber__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>PIVA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;SOC_1&apos;</filterValue>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem9</globalKey>
        <inputFieldName>RelatedAccount_ExternalId__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>accAccRelation</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;SOC_1&apos;</filterValue>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem8</globalKey>
        <inputFieldName>RelatedAccount_ExternalId__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountSoc</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:permissionSetAssignmentUnipolSai LISTSIZE 0 &gt;</formulaConverted>
        <formulaExpression>LISTSIZE(permissionSetAssignmentUnipolSai)&gt;0</formulaExpression>
        <formulaResultPath>UnipolSai</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem7</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>idToSearch</filterValue>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem2</globalKey>
        <inputFieldName>FinServ__Account__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>accAccRelation</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>UserId</filterValue>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem1</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>User</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>User</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem22</globalKey>
        <inputFieldName>accAccRelation:FinServ__RelatedAccount__r.Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Account:nomeSocieta</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem21</globalKey>
        <inputFieldName>Account:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Account:id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem20</globalKey>
        <inputFieldName>accAccRelation:FinServ__Account__r.ExternalId__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Account:codiceFiscale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem19</globalKey>
        <inputFieldName>Account:Identifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Account:codiceAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:accAccRelation:FinServ__Account__r.RecordType.DeveloperName &quot;PersonAccount&quot; = STRINGINDEXOF var:accAccRelation:FinServ__Account__r.FinServ__TaxId__pc var:accAccRelation:FinServ__Account__r.VatNumber__c IF</formulaConverted>
        <formulaExpression>IF(STRINGINDEXOF(accAccRelation:FinServ__Account__r.RecordType.DeveloperName = &quot;PersonAccount&quot;), accAccRelation:FinServ__Account__r.FinServ__TaxId__pc, accAccRelation:FinServ__Account__r.VatNumber__c)</formulaExpression>
        <formulaResultPath>CF1</formulaResultPath>
        <formulaSequence>3.0</formulaSequence>
        <globalKey>uniGetAccountSinistriNewCustom0jI9O000000uxlNUAQItem0</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>uniGetAccountSinistriNew</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;UserId&quot; : &quot;0059X00000Jr08cQAB&quot;,
  &quot;idToSearch&quot; : &quot;0019X000017xaSSQAY&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>uniGetAccountSinistriNew_2</uniqueName>
    <versionNumber>2.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
